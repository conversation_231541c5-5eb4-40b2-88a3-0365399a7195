<?php
/**
 * Color Update Script
 * Updates remaining color references to use the new color scheme
 */

// Define the files to update
$files_to_update = [
    'index.php',
    'dashboard.php',
    'order.php',
    'payment.php',
    'faq.php',
    'auth/login.php',
    'auth/register.php',
    'admin/dashboard.php'
];

// Color mappings
$color_replacements = [
    // CSS classes
    'btn-success' => 'btn-primary',
    'btn-outline-success' => 'btn-outline-primary',
    'text-success' => 'text-primary',
    'bg-success' => 'bg-primary',
    'border-success' => 'border-primary',
    
    // Specific color values
    '#1dbf73' => '#740ae8',
    '#19a463' => '#5c08b8',
    'rgba(29, 191, 115,' => 'rgba(116, 10, 232,',
];

echo "<h2>SparkOn Color Update Script</h2>\n";
echo "<p>Updating color scheme to use #740ae8 (purple) and #0eaaf6 (blue)</p>\n";

foreach ($files_to_update as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $original_content = $content;
        
        // Apply color replacements
        foreach ($color_replacements as $old_color => $new_color) {
            $content = str_replace($old_color, $new_color, $content);
        }
        
        // Save if changes were made
        if ($content !== $original_content) {
            file_put_contents($file, $content);
            echo "<p>✅ Updated: {$file}</p>\n";
        } else {
            echo "<p>ℹ️ No changes needed: {$file}</p>\n";
        }
    } else {
        echo "<p>❌ File not found: {$file}</p>\n";
    }
}

echo "<h3>Color Scheme Updated Successfully!</h3>\n";
echo "<p><strong>Primary Color:</strong> #740ae8 (Purple)</p>\n";
echo "<p><strong>Secondary Color:</strong> #0eaaf6 (Blue)</p>\n";
echo "<p>Please refresh your browser to see the changes.</p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f8f9fa;
}

h2 {
    color: #740ae8;
    border-bottom: 2px solid #0eaaf6;
    padding-bottom: 10px;
}

h3 {
    color: #0eaaf6;
}

p {
    margin: 10px 0;
    padding: 8px;
    border-radius: 4px;
}

p:has-text("✅") {
    background: #d4edda;
    border-left: 4px solid #28a745;
}

p:has-text("❌") {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
}

p:has-text("ℹ️") {
    background: #d1ecf1;
    border-left: 4px solid #17a2b8;
}
</style>