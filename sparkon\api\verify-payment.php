<?php
// Set proper headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include configuration
require_once '../config/config.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Function to log errors
function logError($message, $data = null) {
    $log_message = "[" . date('Y-m-d H:i:s') . "] PAYMENT VERIFICATION: " . $message;
    if ($data) {
        $log_message .= " | Data: " . json_encode($data);
    }
    error_log($log_message);
}

// Function to send JSON response and exit
function sendResponse($success, $message, $data = null) {
    $response = ['success' => $success, 'message' => $message];
    if ($data) {
        $response['data'] = $data;
    }
    echo json_encode($response);
    exit;
}

try {
    // Get and validate input
    $raw_input = file_get_contents('php://input');
    logError("Request received", $raw_input);
    
    if (empty($raw_input)) {
        sendResponse(false, 'No data received');
    }
    
    $input = json_decode($raw_input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        logError("JSON decode error", json_last_error_msg());
        sendResponse(false, 'Invalid JSON data: ' . json_last_error_msg());
    }
    
    // Validate required parameters
    $reference = isset($input['reference']) ? trim($input['reference']) : '';
    $order_id = isset($input['order_id']) ? (int)$input['order_id'] : 0;
    
    if (empty($reference)) {
        logError("Missing reference", $input);
        sendResponse(false, 'Payment reference is required');
    }
    
    if ($order_id <= 0) {
        logError("Invalid order ID", $input);
        sendResponse(false, 'Valid order ID is required');
    }
    
    logError("Processing verification", ['reference' => $reference, 'order_id' => $order_id]);
    
    // Get database connection
    try {
        $db = getDB();
        if (!$db) {
            throw new Exception("Database connection failed");
        }
    } catch (Exception $e) {
        logError("Database connection error", $e->getMessage());
        sendResponse(false, 'Database connection failed');
    }
    
    // Check if order exists and get current status
    $stmt = $db->prepare("SELECT id, payment_status, payment_reference FROM orders WHERE id = ?");
    if (!$stmt->execute([$order_id])) {
        logError("Failed to query order", $order_id);
        sendResponse(false, 'Failed to retrieve order information');
    }
    
    $order = $stmt->fetch();
    if (!$order) {
        logError("Order not found", $order_id);
        sendResponse(false, 'Order not found');
    }
    
    // Check if already paid
    if ($order['payment_status'] === 'paid') {
        logError("Order already paid", $order_id);
        sendResponse(true, 'Payment already processed');
    }
    
    // Verify payment with Paystack
    logError("Starting Paystack verification", $reference);
    $paystack_result = verifyWithPaystack($reference);
    
    if (!$paystack_result['success']) {
        logError("Paystack verification failed", $paystack_result);
        sendResponse(false, $paystack_result['message']);
    }
    
    // Update order status
    logError("Updating order status", $order_id);
    $update_stmt = $db->prepare("
        UPDATE orders 
        SET payment_status = 'paid', 
            payment_reference = ?, 
            status = 'paid',
            updated_at = NOW()
        WHERE id = ?
    ");
    
    if (!$update_stmt->execute([$reference, $order_id])) {
        logError("Failed to update order", ['order_id' => $order_id, 'reference' => $reference]);
        sendResponse(false, 'Failed to update order status');
    }
    
    // Verify the update was successful
    $verify_stmt = $db->prepare("SELECT payment_status FROM orders WHERE id = ?");
    $verify_stmt->execute([$order_id]);
    $updated_order = $verify_stmt->fetch();
    
    if (!$updated_order || $updated_order['payment_status'] !== 'paid') {
        logError("Order update verification failed", $updated_order);
        sendResponse(false, 'Order update verification failed');
    }
    
    logError("Payment verification completed successfully", $order_id);
    
    // Send success response
    sendResponse(true, 'Payment verified and order updated successfully');
    
} catch (Exception $e) {
    logError("Exception occurred", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    sendResponse(false, 'An error occurred during payment verification');
}

/**
 * Verify payment with Paystack API
 */
function verifyWithPaystack($reference) {
    $url = "https://api.paystack.co/transaction/verify/" . urlencode($reference);
    
    // Initialize cURL
    $ch = curl_init();
    
    // Set cURL options
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false, // For local development
        CURLOPT_SSL_VERIFYHOST => false, // For local development
        CURLOPT_HTTPHEADER => [
            "Authorization: Bearer " . PAYSTACK_SECRET_KEY,
            "Content-Type: application/json",
            "Cache-Control: no-cache"
        ]
    ]);
    
    // Execute request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    logError("Paystack API response", [
        'url' => $url,
        'http_code' => $http_code,
        'curl_error' => $curl_error,
        'response' => $response
    ]);
    
    // Check for cURL errors
    if ($curl_error) {
        return [
            'success' => false,
            'message' => 'Network error: ' . $curl_error
        ];
    }
    
    // Check HTTP status
    if ($http_code !== 200) {
        return [
            'success' => false,
            'message' => 'Paystack API returned HTTP ' . $http_code
        ];
    }
    
    // Parse response
    $data = json_decode($response, true);
    if (!$data) {
        return [
            'success' => false,
            'message' => 'Invalid response from Paystack API'
        ];
    }
    
    // Check Paystack response structure
    if (!isset($data['status'])) {
        return [
            'success' => false,
            'message' => 'Invalid Paystack response format'
        ];
    }
    
    // Check if verification was successful
    if ($data['status'] !== true) {
        $error_message = isset($data['message']) ? $data['message'] : 'Payment verification failed';
        return [
            'success' => false,
            'message' => $error_message
        ];
    }
    
    // Check transaction status
    if (!isset($data['data']['status']) || $data['data']['status'] !== 'success') {
        return [
            'success' => false,
            'message' => 'Payment was not successful'
        ];
    }
    
    return [
        'success' => true,
        'data' => $data['data']
    ];
}
?>