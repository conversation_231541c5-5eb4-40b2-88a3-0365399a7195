<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$db = getDB();

// Handle portfolio actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            switch ($action) {
                case 'add_portfolio':
                    $title = sanitize($_POST['title']);
                    $description = sanitize($_POST['description']);
                    $category_id = (int)$_POST['category_id'] ?: null;
                    $project_url = sanitize($_POST['project_url']);
                    $image_url = sanitize($_POST['image_url']);
                    $technologies = $_POST['technologies'] ? json_encode(array_map('trim', explode(',', $_POST['technologies']))) : null;
                    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                    
                    if (empty($title)) {
                        $error = 'Project title is required.';
                    } else {
                        // Handle image upload or URL
                        $image_path = null;
                        
                        // Check if file was uploaded
                        if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                            $upload_dir = '../assets/images/';
                            $file_extension = strtolower(pathinfo($_FILES['image_file']['name'], PATHINFO_EXTENSION));
                            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                            
                            if (in_array($file_extension, $allowed_extensions)) {
                                $new_filename = 'portfolio-' . uniqid() . '.' . $file_extension;
                                $upload_path = $upload_dir . $new_filename;
                                
                                if (move_uploaded_file($_FILES['image_file']['tmp_name'], $upload_path)) {
                                    $image_path = 'assets/images/' . $new_filename;
                                } else {
                                    $error = 'Failed to upload image file.';
                                }
                            } else {
                                $error = 'Invalid image format. Please use JPG, PNG, GIF, or WebP.';
                            }
                        } elseif (!empty($image_url)) {
                            // Use provided URL
                            $image_path = $image_url;
                        }
                        
                        if (!isset($error)) {
                            $stmt = $db->prepare("INSERT INTO portfolio (title, description, category_id, project_url, image, technologies, is_featured) VALUES (?, ?, ?, ?, ?, ?, ?)");
                            $stmt->execute([$title, $description, $category_id, $project_url, $image_path, $technologies, $is_featured]);
                            $success = 'Portfolio item added successfully.';
                        }
                    }
                    break;
                    
                case 'edit_portfolio':
                    $portfolio_id = (int)$_POST['portfolio_id'];
                    $title = sanitize($_POST['title']);
                    $description = sanitize($_POST['description']);
                    $category_id = (int)$_POST['category_id'] ?: null;
                    $project_url = sanitize($_POST['project_url']);
                    $image_url = sanitize($_POST['image_url']);
                    $technologies = $_POST['technologies'] ? json_encode(array_map('trim', explode(',', $_POST['technologies']))) : null;
                    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                    
                    if (empty($title)) {
                        $error = 'Project title is required.';
                    } else {
                        // Get current image
                        $current_stmt = $db->prepare("SELECT image FROM portfolio WHERE id = ?");
                        $current_stmt->execute([$portfolio_id]);
                        $current_image = $current_stmt->fetch()['image'];
                        
                        $image_path = $current_image; // Keep current image by default
                        
                        // Check if new file was uploaded
                        if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                            $upload_dir = '../assets/images/';
                            $file_extension = strtolower(pathinfo($_FILES['image_file']['name'], PATHINFO_EXTENSION));
                            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                            
                            if (in_array($file_extension, $allowed_extensions)) {
                                $new_filename = 'portfolio-' . uniqid() . '.' . $file_extension;
                                $upload_path = $upload_dir . $new_filename;
                                
                                if (move_uploaded_file($_FILES['image_file']['tmp_name'], $upload_path)) {
                                    // Delete old image if it exists and is not a URL
                                    if ($current_image && !filter_var($current_image, FILTER_VALIDATE_URL) && file_exists('../' . $current_image)) {
                                        unlink('../' . $current_image);
                                    }
                                    $image_path = 'assets/images/' . $new_filename;
                                } else {
                                    $error = 'Failed to upload image file.';
                                }
                            } else {
                                $error = 'Invalid image format. Please use JPG, PNG, GIF, or WebP.';
                            }
                        } elseif (!empty($image_url) && $image_url !== $current_image) {
                            // Use new URL
                            $image_path = $image_url;
                        }
                        
                        if (!isset($error)) {
                            $stmt = $db->prepare("UPDATE portfolio SET title = ?, description = ?, category_id = ?, project_url = ?, image = ?, technologies = ?, is_featured = ? WHERE id = ?");
                            $stmt->execute([$title, $description, $category_id, $project_url, $image_path, $technologies, $is_featured, $portfolio_id]);
                            $success = 'Portfolio item updated successfully.';
                        }
                    }
                    break;
                    
                case 'delete_portfolio':
                    $portfolio_id = (int)$_POST['portfolio_id'];
                    $stmt = $db->prepare("DELETE FROM portfolio WHERE id = ?");
                    $stmt->execute([$portfolio_id]);
                    $success = 'Portfolio item deleted successfully.';
                    break;
                    
                case 'toggle_featured':
                    $portfolio_id = (int)$_POST['portfolio_id'];
                    $stmt = $db->prepare("UPDATE portfolio SET is_featured = NOT is_featured WHERE id = ?");
                    $stmt->execute([$portfolio_id]);
                    $success = 'Featured status updated successfully.';
                    break;
            }
        } catch (Exception $e) {
            $error = 'Failed to perform action: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$featured_filter = isset($_GET['featured']) ? sanitize($_GET['featured']) : '';

// Build query with filters
$where_conditions = ['1=1'];
$params = [];

if ($category_filter) {
    $where_conditions[] = 'p.category_id = ?';
    $params[] = $category_filter;
}

if ($featured_filter !== '') {
    $where_conditions[] = 'p.is_featured = ?';
    $params[] = $featured_filter === 'yes' ? 1 : 0;
}

$where_clause = implode(' AND ', $where_conditions);

// Get portfolio items with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

$portfolio_stmt = $db->prepare("
    SELECT p.*, c.name as category_name 
    FROM portfolio p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE {$where_clause}
    ORDER BY p.is_featured DESC, p.created_at DESC 
    LIMIT {$per_page} OFFSET {$offset}
");
$portfolio_stmt->execute($params);
$portfolio_items = $portfolio_stmt->fetchAll();

// Get total count for pagination
$count_stmt = $db->prepare("SELECT COUNT(*) as total FROM portfolio p WHERE {$where_clause}");
$count_stmt->execute($params);
$total_items = $count_stmt->fetch()['total'];
$total_pages = ceil($total_items / $per_page);

// Get categories for dropdown
$categories_stmt = $db->prepare("SELECT * FROM categories ORDER BY name");
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll();

// Get portfolio statistics
$stats_stmt = $db->prepare("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_featured = 1 THEN 1 END) as featured,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent
    FROM portfolio
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Management - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #740ae8 0%, #0eaaf6 100%);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .portfolio-card {
            transition: transform 0.3s ease;
        }
        
        .portfolio-card:hover {
            transform: translateY(-5px);
        }
        
            </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <span style="color: #0eaaf6;">Spark</span>On
                        </h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                        </a>
                        <a class="nav-link" href="categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                        <a class="nav-link active" href="portfolio.php">
                            <i class="fas fa-briefcase me-2"></i>Portfolio
                        </a>
                        <a class="nav-link" href="testimonials.php">
                            <i class="fas fa-star me-2"></i>Testimonials
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Header -->
                <div class="admin-header p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-0">Portfolio Management</h2>
                            <small class="text-muted">Manage showcase projects and case studies</small>
                        </div>
                        <div>
                            <button class="btn btn-success" onclick="addPortfolio()">
                                <i class="fas fa-plus me-2"></i>Add Project
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-4 col-md-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-briefcase fa-2x text-primary mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['total']; ?></h4>
                                <small class="text-muted">Total Projects</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['featured']; ?></h4>
                                <small class="text-muted">Featured</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['recent']; ?></h4>
                                <small class="text-muted">Recent (30 days)</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Filter by Category</label>
                                <select class="form-select" name="category">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" 
                                                <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo $category['name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Featured Status</label>
                                <select class="form-select" name="featured">
                                    <option value="">All Projects</option>
                                    <option value="yes" <?php echo $featured_filter === 'yes' ? 'selected' : ''; ?>>Featured Only</option>
                                    <option value="no" <?php echo $featured_filter === 'no' ? 'selected' : ''; ?>>Non-Featured</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-filter me-1"></i>Filter
                                </button>
                                <a href="portfolio.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                            <div class="col-md-2 d-flex align-items-end justify-content-end">
                                <small class="text-muted"><?php echo $total_items; ?> projects found</small>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Portfolio Grid -->
                <div class="row g-4">
                    <?php foreach ($portfolio_items as $item): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="card portfolio-card shadow-sm h-100">
                                <div class="portfolio-image position-relative" style="height: 200px; overflow: hidden;">
                                    <?php if ($item['image']): ?>
                                        <img src="<?php echo htmlspecialchars($item['image']); ?>" 
                                             alt="<?php echo htmlspecialchars($item['title']); ?>" 
                                             class="img-fluid w-100 h-100" 
                                             style="object-fit: cover;"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="d-none align-items-center justify-content-center h-100 bg-light text-muted" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
                                            <i class="fas fa-image fa-3x"></i>
                                        </div>
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center h-100 bg-light text-muted">
                                            <i class="fas fa-image fa-3x"></i>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($item['is_featured']): ?>
                                        <span class="position-absolute top-0 start-0 badge bg-warning m-2">
                                            <i class="fas fa-star me-1"></i>Featured
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="fw-bold mb-0"><?php echo htmlspecialchars(substr($item['title'], 0, 30)) . (strlen($item['title']) > 30 ? '...' : ''); ?></h6>
                                        <?php if ($item['category_name']): ?>
                                            <span class="badge bg-primary"><?php echo $item['category_name']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <p class="text-muted small mb-3">
                                        <?php echo htmlspecialchars(substr($item['description'], 0, 80)) . (strlen($item['description']) > 80 ? '...' : ''); ?>
                                    </p>
                                    
                                    <?php if ($item['technologies']): ?>
                                        <div class="mb-3">
                                            <?php 
                                            $technologies = json_decode($item['technologies'], true);
                                            if ($technologies) {
                                                foreach (array_slice($technologies, 0, 3) as $tech): 
                                            ?>
                                                <span class="badge bg-light text-dark me-1"><?php echo htmlspecialchars($tech); ?></span>
                                            <?php 
                                                endforeach;
                                                if (count($technologies) > 3) {
                                                    echo '<span class="badge bg-light text-dark">+' . (count($technologies) - 3) . '</span>';
                                                }
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <?php echo date('M j, Y', strtotime($item['created_at'])); ?>
                                        </small>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editPortfolio(<?php echo htmlspecialchars(json_encode($item)); ?>)" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="toggleFeatured(<?php echo $item['id']; ?>)" title="Toggle Featured">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deletePortfolio(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['title']); ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <?php if (empty($portfolio_items)): ?>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No portfolio items found</h5>
                                <p class="text-muted mb-3">
                                    <?php if ($category_filter || $featured_filter !== ''): ?>
                                        Try adjusting your filters or add new portfolio items.
                                    <?php else: ?>
                                        Start showcasing your work by adding your first portfolio item.
                                    <?php endif; ?>
                                </p>
                                <button class="btn btn-success" onclick="addPortfolio()">
                                    <i class="fas fa-plus me-2"></i>Add Portfolio Item
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Portfolio pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&category=<?php echo $category_filter; ?>&featured=<?php echo $featured_filter; ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&category=<?php echo $category_filter; ?>&featured=<?php echo $featured_filter; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&category=<?php echo $category_filter; ?>&featured=<?php echo $featured_filter; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add/Edit Portfolio Modal -->
    <div class="modal fade" id="portfolioModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="portfolioModalTitle">Add Portfolio Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="portfolioForm" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="portfolioAction" value="add_portfolio">
                        <input type="hidden" name="portfolio_id" id="portfolioId">
                        
                        <div class="row g-3">
                            <div class="col-md-8">
                                <label class="form-label">Project Title *</label>
                                <input type="text" class="form-control" name="title" id="portfolioTitle" required>
                            </div>
                            
                            <div class="col-md-4">
                                <label class="form-label">Category</label>
                                <select class="form-select" name="category_id" id="portfolioCategory">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" id="portfolioDescription" rows="4" 
                                          placeholder="Describe the project, challenges, and solutions..."></textarea>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Project URL</label>
                                <input type="url" class="form-control" name="project_url" id="portfolioUrl" 
                                       placeholder="https://example.com">
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Technologies Used</label>
                                <input type="text" class="form-control" name="technologies" id="portfolioTechnologies" 
                                       placeholder="PHP, MySQL, Bootstrap, JavaScript">
                                <div class="form-text">Separate multiple technologies with commas</div>
                            </div>
                            
                            <div class="col-12">
                                <label class="form-label">Project Image</label>
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <label class="form-label small">Upload Image File</label>
                                        <input type="file" class="form-control" name="image_file" id="portfolioImageFile" 
                                               accept="image/*" onchange="handleImageUpload()">
                                        <div class="form-text">JPG, PNG, GIF, WebP (Max 5MB)</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label small">Or Image URL</label>
                                        <input type="url" class="form-control" name="image_url" id="portfolioImageUrl" 
                                               placeholder="https://example.com/image.jpg" onchange="handleImageUrl()">
                                        <div class="form-text">Direct link to image</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div id="imagePreview" class="d-none">
                                        <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-height: 150px;">
                                        <button type="button" class="btn btn-sm btn-outline-danger ms-2" onclick="clearImagePreview()">
                                            <i class="fas fa-times"></i> Remove
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_featured" id="portfolioFeatured">
                                    <label class="form-check-label" for="portfolioFeatured">
                                        Featured Project (will be highlighted on homepage)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success" id="portfolioSubmitBtn">Add Project</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the portfolio item "<span id="deletePortfolioTitle"></span>"?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete_portfolio">
                        <input type="hidden" name="portfolio_id" id="deletePortfolioId">
                        <button type="submit" class="btn btn-danger">Delete Project</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addPortfolio() {
            document.getElementById('portfolioModalTitle').textContent = 'Add Portfolio Item';
            document.getElementById('portfolioAction').value = 'add_portfolio';
            document.getElementById('portfolioSubmitBtn').textContent = 'Add Project';
            document.getElementById('portfolioForm').reset();
            
            const modal = new bootstrap.Modal(document.getElementById('portfolioModal'));
            modal.show();
        }
        
        function editPortfolio(item) {
            document.getElementById('portfolioModalTitle').textContent = 'Edit Portfolio Item';
            document.getElementById('portfolioAction').value = 'edit_portfolio';
            document.getElementById('portfolioSubmitBtn').textContent = 'Update Project';
            document.getElementById('portfolioId').value = item.id;
            document.getElementById('portfolioTitle').value = item.title;
            document.getElementById('portfolioDescription').value = item.description || '';
            document.getElementById('portfolioCategory').value = item.category_id || '';
            document.getElementById('portfolioUrl').value = item.project_url || '';
            document.getElementById('portfolioImageUrl').value = item.image || '';
            document.getElementById('portfolioFeatured').checked = item.is_featured == 1;
            
            // Handle technologies
            if (item.technologies) {
                const technologies = JSON.parse(item.technologies);
                document.getElementById('portfolioTechnologies').value = technologies.join(', ');
            } else {
                document.getElementById('portfolioTechnologies').value = '';
            }
            
            // Show current image preview if exists
            if (item.image) {
                showImagePreview(item.image);
            } else {
                clearImagePreview();
            }
            
            const modal = new bootstrap.Modal(document.getElementById('portfolioModal'));
            modal.show();
        }
        
        function deletePortfolio(portfolioId, portfolioTitle) {
            document.getElementById('deletePortfolioId').value = portfolioId;
            document.getElementById('deletePortfolioTitle').textContent = portfolioTitle;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
        
        function toggleFeatured(portfolioId) {
            if (confirm('Toggle featured status for this portfolio item?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_featured">
                    <input type="hidden" name="portfolio_id" value="${portfolioId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Form validation
        document.getElementById('portfolioForm').addEventListener('submit', function(e) {
            const title = document.getElementById('portfolioTitle').value.trim();
            if (!title) {
                e.preventDefault();
                alert('Project title is required.');
                return;
            }
        });
        
        // Image handling functions
        function handleImageUpload() {
            const fileInput = document.getElementById('portfolioImageFile');
            const urlInput = document.getElementById('portfolioImageUrl');
            
            if (fileInput.files && fileInput.files[0]) {
                // Clear URL input when file is selected
                urlInput.value = '';
                
                // Validate file size (5MB limit)
                if (fileInput.files[0].size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    fileInput.value = '';
                    return;
                }
                
                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    showImagePreview(e.target.result);
                };
                reader.readAsDataURL(fileInput.files[0]);
            }
        }
        
        function handleImageUrl() {
            const urlInput = document.getElementById('portfolioImageUrl');
            const fileInput = document.getElementById('portfolioImageFile');
            
            if (urlInput.value) {
                // Clear file input when URL is entered
                fileInput.value = '';
                showImagePreview(urlInput.value);
            } else {
                clearImagePreview();
            }
        }
        
        function showImagePreview(src) {
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImg');
            
            img.src = src;
            preview.classList.remove('d-none');
        }
        
        function clearImagePreview() {
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImg');
            const fileInput = document.getElementById('portfolioImageFile');
            const urlInput = document.getElementById('portfolioImageUrl');
            
            img.src = '';
            preview.classList.add('d-none');
            fileInput.value = '';
            urlInput.value = '';
        }
    </script>
</body>
</html>