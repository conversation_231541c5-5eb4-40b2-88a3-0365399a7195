<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

// Get order ID from URL
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

if (!$order_id) {
    redirect('dashboard.php');
}

$db = getDB();

// Get order details
$stmt = $db->prepare("
    SELECT o.*, c.name as category_name, u.full_name, u.email 
    FROM orders o 
    LEFT JOIN categories c ON o.category_id = c.id 
    LEFT JOIN users u ON o.user_id = u.id 
    WHERE o.id = ? AND o.user_id = ?
");
$stmt->execute([$order_id, $_SESSION['user_id']]);
$order = $stmt->fetch();

if (!$order) {
    redirect('dashboard.php');
}

// Check if already paid
if ($order['payment_status'] === 'paid') {
    redirect("order-tracking.php?order_id={$order_id}");
}

// Calculate fees (platform takes 5% + payment processing fee)
$platform_fee = $order['budget'] * 0.05; // 5% platform fee
$paystack_fee = ($order['budget'] + $platform_fee) * 0.015; // 1.5% Paystack fee
$total_amount = $order['budget'] + $platform_fee + $paystack_fee;

// Generate payment reference
$payment_reference = 'SPARK_' . $order_id . '_' . time();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Paystack Inline JS -->
    <script src="https://js.paystack.co/v1/inline.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Debug Notice -->
                <div class="alert alert-warning mb-4">
                    <i class="fas fa-bug me-2"></i>
                    <strong>Debug Mode:</strong> This is a test version with enhanced debugging. Check browser console for detailed logs.
                </div>

                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="fw-bold">Test Payment (Debug Mode)</h1>
                    <p class="text-muted">Enhanced debugging enabled</p>
                </div>

                <div class="row g-4">
                    <!-- Order Summary -->
                    <div class="col-lg-7">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-alt me-2"></i>Order Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6 class="fw-bold"><?php echo htmlspecialchars($order['project_title']); ?></h6>
                                    <?php if ($order['category_name']): ?>
                                        <span class="badge bg-success"><?php echo $order['category_name']; ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>Description:</strong>
                                    <p class="text-muted mb-0"><?php echo nl2br(htmlspecialchars(substr($order['project_description'], 0, 200))); ?>...</p>
                                </div>
                                
                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <strong>Budget:</strong>
                                        <div class="text-success fw-bold"><?php echo formatCurrency($order['budget']); ?></div>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Deadline:</strong>
                                        <div><?php echo date('M j, Y', strtotime($order['deadline'])); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    <div class="col-lg-5">
                        <div class="card shadow-sm">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-credit-card me-2"></i>Test Payment
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="payment-breakdown">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Project Budget:</span>
                                        <span><?php echo formatCurrency($order['budget']); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Platform Fee (5%):</span>
                                        <span><?php echo formatCurrency($platform_fee); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-3">
                                        <span>Payment Processing:</span>
                                        <span><?php echo formatCurrency($paystack_fee); ?></span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between mb-4">
                                        <strong>Total Amount:</strong>
                                        <strong class="text-success"><?php echo formatCurrency($total_amount); ?></strong>
                                    </div>
                                </div>
                                
                                <button class="btn btn-warning btn-lg w-100 mb-3" onclick="payWithPaystack()">
                                    <i class="fas fa-bug me-2"></i>Test Pay Now
                                </button>
                                
                                <div class="text-center">
                                    <small class="text-muted">
                                        Debug mode - Enhanced logging enabled
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Debug Info -->
                        <div class="card mt-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">Debug Information</h6>
                            </div>
                            <div class="card-body">
                                <small>
                                    <strong>Order ID:</strong> <?php echo $order_id; ?><br>
                                    <strong>Payment Reference:</strong> <?php echo $payment_reference; ?><br>
                                    <strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?><br>
                                    <strong>Email:</strong> <?php echo $order['email']; ?><br>
                                    <strong>Amount (Kobo):</strong> <?php echo (int)($total_amount * 100); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Debug Log -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">Debug Log</h6>
                            </div>
                            <div class="card-body">
                                <div id="debugLog" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                                    <div>Debug log will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Back Button -->
                <div class="text-center mt-4">
                    <a href="payment.php?order_id=<?php echo $order_id; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Normal Payment
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addDebugLog(message, data = null) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            if (data) {
                logEntry.innerHTML += `<br>&nbsp;&nbsp;&nbsp;&nbsp;${JSON.stringify(data, null, 2)}`;
            }
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
            
            // Also log to console
            console.log(`[DEBUG] ${message}`, data);
        }
        
        function payWithPaystack() {
            addDebugLog('Initializing Paystack payment...');
            
            let handler = PaystackPop.setup({
                key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
                email: '<?php echo $order['email']; ?>',
                amount: <?php echo (int)($total_amount * 100); ?>,
                currency: 'GHS',
                ref: '<?php echo $payment_reference; ?>',
                metadata: {
                    order_id: <?php echo $order_id; ?>,
                    user_id: <?php echo $_SESSION['user_id']; ?>,
                    project_title: '<?php echo addslashes($order['project_title']); ?>'
                },
                callback: function(response) {
                    addDebugLog('Paystack payment callback received', response);
                    verifyPayment(response.reference);
                },
                onClose: function() {
                    addDebugLog('Paystack payment modal closed');
                }
            });
            
            addDebugLog('Opening Paystack payment modal...');
            handler.openIframe();
        }
        
        function verifyPayment(reference) {
            addDebugLog('Starting payment verification', {reference: reference});
            
            // Show loading
            const payButton = document.querySelector('button[onclick="payWithPaystack()"]');
            const originalText = payButton.innerHTML;
            payButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Verifying Payment...';
            payButton.disabled = true;
            
            // Send verification request to debug endpoint
            fetch('debug-verify-payment.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reference: reference,
                    order_id: <?php echo $order['id']; ?>
                })
            })
            .then(response => {
                addDebugLog('Received response from server', {status: response.status, ok: response.ok});
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                addDebugLog('Verification response parsed', data);
                
                if (data.success) {
                    // Payment verified successfully
                    addDebugLog('Payment verification successful!');
                    payButton.innerHTML = '<i class="fas fa-check me-2"></i>Payment Verified!';
                    payButton.className = 'btn btn-success btn-lg w-100 mb-3';
                    
                    setTimeout(() => {
                        addDebugLog('Redirecting to order tracking...');
                        window.location.href = 'order-tracking.php?order_id=<?php echo $order_id; ?>&payment=success';
                    }, 2000);
                } else {
                    // Payment verification failed
                    const errorMessage = data.message || 'Payment verification failed. Please contact support.';
                    addDebugLog('Payment verification failed', {error: errorMessage, debug: data.debug});
                    
                    alert(`Payment Verification Failed\n\nError: ${errorMessage}\n\nCheck the debug log for more details.`);
                    
                    payButton.innerHTML = originalText;
                    payButton.disabled = false;
                }
            })
            .catch(error => {
                addDebugLog('Network error during verification', error.toString());
                
                alert(`Network Error\n\nFailed to verify payment due to a network error.\n\nPayment Reference: ${reference}\n\nCheck the debug log for more details.`);
                
                payButton.innerHTML = originalText;
                payButton.disabled = false;
            });
        }
        
        // Initialize debug log
        addDebugLog('Test payment page loaded');
        addDebugLog('Configuration', {
            order_id: <?php echo $order_id; ?>,
            payment_reference: '<?php echo $payment_reference; ?>',
            total_amount: <?php echo $total_amount; ?>,
            amount_kobo: <?php echo (int)($total_amount * 100); ?>
        });
    </script>
</body>
</html>