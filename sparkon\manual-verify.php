<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $reference = sanitize($_POST['reference'] ?? '');
    $order_id = (int)($_POST['order_id'] ?? 0);
    
    if (empty($reference) || !$order_id) {
        $error = 'Please provide both payment reference and order ID.';
    } else {
        $db = getDB();
        
        // Check if order belongs to user
        $stmt = $db->prepare("SELECT * FROM orders WHERE id = ? AND user_id = ?");
        $stmt->execute([$order_id, $_SESSION['user_id']]);
        $order = $stmt->fetch();
        
        if (!$order) {
            $error = 'Order not found or does not belong to you.';
        } else if ($order['payment_status'] === 'paid') {
            $message = 'This order is already marked as paid.';
        } else {
            // Try to verify with Paystack
            $verification = verifyPaystackPayment($reference);
            
            if ($verification['success']) {
                // Update order
                $stmt = $db->prepare("
                    UPDATE orders 
                    SET payment_status = 'paid', 
                        payment_reference = ?, 
                        status = 'paid',
                        updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ");
                $stmt->execute([$reference, $order_id]);
                
                $message = 'Payment verified successfully! Your order has been updated.';
                
                // Redirect to order tracking after 3 seconds
                header("refresh:3;url=order-tracking.php?order_id=$order_id&payment=success");
            } else {
                $error = 'Payment verification failed: ' . ($verification['message'] ?? 'Unknown error');
            }
        }
    }
}

/**
 * Verify payment with Paystack API
 */
function verifyPaystackPayment($reference) {
    $url = "https://api.paystack.co/transaction/verify/" . $reference;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer " . PAYSTACK_SECRET_KEY,
        "Content-Type: application/json"
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        return ['success' => false, 'message' => 'Network error occurred'];
    }
    
    if ($http_code === 200) {
        $data = json_decode($response, true);
        
        if (!$data) {
            return ['success' => false, 'message' => 'Invalid response format'];
        }
        
        if (isset($data['status']) && $data['status'] === true && 
            isset($data['data']['status']) && $data['data']['status'] === 'success') {
            return ['success' => true, 'data' => $data['data']];
        } else {
            $message = isset($data['message']) ? $data['message'] : 'Payment verification failed';
            return ['success' => false, 'message' => $message];
        }
    } else {
        return ['success' => false, 'message' => "HTTP Error: $http_code"];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Payment Verification - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-search me-2"></i>Manual Payment Verification
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <p class="text-muted mb-4">
                            If your payment was successful but the system didn't verify it automatically, 
                            you can manually verify it here using your payment reference.
                        </p>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="order_id" class="form-label">Order ID</label>
                                <input type="number" class="form-control" id="order_id" name="order_id" 
                                       value="<?php echo $_GET['order_id'] ?? ''; ?>" required>
                                <div class="form-text">The order ID you're trying to verify payment for</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="reference" class="form-label">Payment Reference</label>
                                <input type="text" class="form-control" id="reference" name="reference" 
                                       placeholder="e.g., SPARK_123_1234567890" required>
                                <div class="form-text">The payment reference from your payment confirmation</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>Verify Payment
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Help Section -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="fw-bold">Need Help?</h6>
                        <p class="text-muted mb-3">
                            If you're having trouble finding your payment reference or the verification still fails:
                        </p>
                        <ul class="text-muted">
                            <li>Check your email for the payment confirmation</li>
                            <li>Look for the reference in your bank statement</li>
                            <li>Contact our support team at <?php echo SITE_EMAIL; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>