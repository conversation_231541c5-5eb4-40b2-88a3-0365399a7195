# SparkOn - Company Portfolio Website

A complete Fiverr-inspired company portfolio website with project ordering, tracking, and management system. Built with PHP, MySQL, Bootstrap, and JavaScript.

## 🚀 Features

### Frontend Features
- **Responsive Design**: Mobile-first design that works on all devices
- **Modern UI/UX**: Clean, professional interface inspired by Fiverr
- **Interactive Elements**: Smooth animations, hover effects, and transitions
- **Search Functionality**: Search services and FAQ
- **Live Chat Bot**: AI-powered chatbot for customer support
- **Contact Forms**: Multiple contact methods with form validation

### User Features
- **User Registration/Login**: Secure authentication system
- **Project Submission**: Detailed project request forms
- **Payment Integration**: Secure payments via Paystack
- **Order Tracking**: Real-time project status tracking
- **User Dashboard**: Personal dashboard with order history
- **File Uploads**: Support for project files and attachments

### Admin Features
- **Admin Dashboard**: Comprehensive analytics and statistics
- **Order Management**: View, update, and manage all orders
- **User Management**: Manage client accounts
- **Contact Management**: Handle contact form submissions
- **Content Management**: Manage categories, portfolio, testimonials
- **Revenue Tracking**: Financial reports and analytics

### Technical Features
- **Secure**: CSRF protection, input validation, password hashing
- **SEO Optimized**: Clean URLs, meta tags, structured data
- **Performance**: Optimized images, minified assets, caching
- **Database**: Well-structured MySQL database with relationships
- **API Endpoints**: RESTful API for AJAX functionality

## 🛠️ Tech Stack

- **Frontend**: HTML5, CSS3, Bootstrap 5, JavaScript (ES6+)
- **Backend**: PHP 8+, MySQL
- **Payment**: Paystack Integration
- **Authentication**: PHP Sessions with security features
- **Charts**: Chart.js for analytics
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Macan)

## 📋 Requirements

- **Web Server**: Apache/Nginx
- **PHP**: Version 8.0 or higher
- **MySQL**: Version 5.7 or higher
- **Extensions**: PDO, cURL, GD, mbstring
- **Composer**: For dependency management (optional)

## 🔧 Installation

### 1. Clone/Download the Project
```bash
# If using Git
git clone https://github.com/yourusername/sparkon.git

# Or download and extract the ZIP file to your web server directory
```

### 2. Database Setup
1. Create a new MySQL database named `sparkon`
2. Import the database schema:
   ```bash
   mysql -u your_username -p sparkon < database/sparkon.sql
   ```
   Or use phpMyAdmin to import the `database/sparkon.sql` file

### 3. Configuration
1. Open `config/config.php`
2. Update the database credentials:
   ```php
   // In config/database.php
   private $host = 'localhost';
   private $db_name = 'sparkon';
   private $username = 'your_db_username';
   private $password = 'your_db_password';
   ```

3. Configure Paystack (for payments):
   ```php
   // In config/config.php
   define('PAYSTACK_PUBLIC_KEY', 'pk_test_your_paystack_public_key');
   define('PAYSTACK_SECRET_KEY', 'sk_test_your_paystack_secret_key');
   ```

4. Update site settings:
   ```php
   define('SITE_URL', 'http://your-domain.com/sparkon');
   define('SITE_EMAIL', '<EMAIL>');
   define('SITE_PHONE', '+233 123 456 789');
   ```

### 4. File Permissions
Set proper permissions for upload directories:
```bash
chmod 755 uploads/
chmod 755 uploads/projects/
```

### 5. Web Server Configuration

#### Apache (.htaccess)
The project includes `.htaccess` files for clean URLs and security.

#### Nginx
Add this to your Nginx configuration:
```nginx
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

location ~ \.php$ {
    fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
}
```

## 🎯 Usage

### Default Admin Account
- **Email**: <EMAIL>
- **Password**: selables_GH

### User Registration
Users can register through the `/auth/register.php` page or the "Join Now" button.

### Project Workflow
1. **User Registration**: Client creates an account
2. **Project Submission**: Client fills out project request form
3. **Payment**: Secure payment via Paystack
4. **Project Assignment**: Admin assigns project to team
5. **Progress Updates**: Real-time status updates
6. **Delivery**: Completed project delivered to client

### Admin Functions
- **Dashboard**: Overview of all activities and statistics
- **Orders**: Manage all project orders and update statuses
- **Users**: View and manage client accounts
- **Contacts**: Handle contact form submissions
- **Content**: Manage categories, portfolio items, testimonials

## 🔐 Security Features

- **Password Hashing**: bcrypt for secure password storage
- **CSRF Protection**: Tokens for form submissions
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Prevention**: Prepared statements
- **XSS Protection**: Output escaping
- **File Upload Security**: Type and size validation
- **Session Security**: Secure session handling

## 📱 Responsive Design

The website is fully responsive and tested on:
- **Desktop**: 1920px and above
- **Laptop**: 1366px - 1919px
- **Tablet**: 768px - 1365px
- **Mobile**: 320px - 767px

## 🎨 Customization

### Colors
Main colors are defined in CSS variables:
```css
:root {
    --primary-color: #1dbf73;
    --primary-dark: #19a463;
    --secondary-color: #ffc107;
    /* ... */
}
```

### Logo
Replace the text logo in the navigation with your own:
```html
<a class="navbar-brand fw-bold" href="index.php">
    <img src="assets/images/logo.png" alt="Your Logo" height="40">
</a>
```

### Content
- Update company information in `config/config.php`
- Modify homepage content in `index.php`
- Add your own portfolio items via admin panel
- Customize FAQ content in the database

## 🔧 API Endpoints

- **POST** `/api/contact.php` - Handle contact form submissions
- **POST** `/api/chatbot.php` - Chatbot responses
- **POST** `/api/verify-payment.php` - Verify Paystack payments

## 📊 Database Schema

### Main Tables
- **users**: User accounts (clients and admins)
- **orders**: Project orders and requests
- **categories**: Service categories
- **contacts**: Contact form submissions
- **testimonials**: Client testimonials
- **portfolio**: Portfolio/project showcase
- **faqs**: Frequently asked questions
- **chat_messages**: Chatbot conversation history

## 🚀 Deployment

### Production Checklist
1. **Security**: Change default admin password
2. **SSL**: Enable HTTPS for secure payments
3. **Environment**: Set production environment variables
4. **Backups**: Set up automated database backups
5. **Monitoring**: Configure error logging
6. **Performance**: Enable caching and compression
7. **SEO**: Submit sitemap to search engines

### Environment Variables
For production, consider using environment variables:
```php
$db_host = $_ENV['DB_HOST'] ?? 'localhost';
$db_name = $_ENV['DB_NAME'] ?? 'sparkon';
$paystack_key = $_ENV['PAYSTACK_SECRET_KEY'];
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **File Upload Issues**
   - Check folder permissions (755 for directories)
   - Verify `upload_max_filesize` in php.ini
   - Ensure `uploads/` directory exists

3. **Payment Issues**
   - Verify Paystack API keys
   - Check if cURL is enabled
   - Ensure SSL is configured for production

4. **Email Not Sending**
   - Configure SMTP settings in `config/config.php`
   - Check server mail configuration
   - Verify firewall settings

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: Check the code comments
- **Issues**: Create an issue in the repository

## 📄 License

This project is licensed under the MIT License. See the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 🙏 Acknowledgments

- **Bootstrap**: For the responsive framework
- **Font Awesome**: For the beautiful icons
- **Chart.js**: For the analytics charts
- **Paystack**: For payment processing
- **Fiverr**: For design inspiration

---

**Built with ❤️ by the SparkOn Team**