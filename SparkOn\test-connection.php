<?php
/**
 * Database Connection Test
 * Use this file to test your database connection after deployment
 * DELETE THIS FILE after successful testing for security!
 */

// Include configuration
require_once 'config/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - SparkOn</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">SparkOn - Database Connection Test</h4>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            // Test database connection
                            $db = getDB();
                            
                            if ($db) {
                                echo '<div class="alert alert-success">';
                                echo '<h5><i class="fas fa-check-circle"></i> Database Connection Successful!</h5>';
                                echo '<p>Your database connection is working properly.</p>';
                                
                                // Test if tables exist
                                $stmt = $db->query("SHOW TABLES");
                                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                
                                if (count($tables) > 0) {
                                    echo '<h6>Database Tables Found:</h6>';
                                    echo '<ul>';
                                    foreach ($tables as $table) {
                                        echo '<li>' . htmlspecialchars($table) . '</li>';
                                    }
                                    echo '</ul>';
                                } else {
                                    echo '<div class="alert alert-warning mt-3">';
                                    echo '<strong>Warning:</strong> No tables found. Make sure to import the database/sparkon.sql file.';
                                    echo '</div>';
                                }
                                echo '</div>';
                                
                                // Test admin user exists
                                $stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
                                $stmt->execute();
                                $adminCount = $stmt->fetch()['count'];
                                
                                if ($adminCount > 0) {
                                    echo '<div class="alert alert-info">';
                                    echo '<strong>Admin Account:</strong> ' . $adminCount . ' admin user(s) found.';
                                    echo '</div>';
                                } else {
                                    echo '<div class="alert alert-warning">';
                                    echo '<strong>Warning:</strong> No admin users found. You may need to create an admin account.';
                                    echo '</div>';
                                }
                                
                            } else {
                                echo '<div class="alert alert-danger">';
                                echo '<h5><i class="fas fa-times-circle"></i> Database Connection Failed!</h5>';
                                echo '<p>Could not connect to the database. Please check your configuration.</p>';
                                echo '</div>';
                            }
                            
                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">';
                            echo '<h5><i class="fas fa-times-circle"></i> Database Connection Error!</h5>';
                            echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                            echo '<hr>';
                            echo '<h6>Common Solutions:</h6>';
                            echo '<ul>';
                            echo '<li>Check database credentials in config/database.php</li>';
                            echo '<li>Ensure the database exists</li>';
                            echo '<li>Verify database user has proper permissions</li>';
                            echo '<li>Check if MySQL service is running</li>';
                            echo '</ul>';
                            echo '</div>';
                        }
                        ?>
                        
                        <hr>
                        
                        <h5>Configuration Check</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Site URL:</strong> <?php echo SITE_URL; ?><br>
                                <strong>Site Email:</strong> <?php echo SITE_EMAIL; ?><br>
                                <strong>Site Phone:</strong> <?php echo SITE_PHONE; ?><br>
                            </div>
                            <div class="col-md-6">
                                <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
                                <strong>Upload Directory:</strong> <?php echo UPLOAD_DIR; ?><br>
                                <strong>Max File Size:</strong> <?php echo number_format(MAX_FILE_SIZE / 1024 / 1024, 1); ?>MB<br>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <h5>PHP Extensions Check</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <span class="badge <?php echo extension_loaded('pdo') ? 'bg-success' : 'bg-danger'; ?>">
                                    PDO: <?php echo extension_loaded('pdo') ? 'OK' : 'Missing'; ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <span class="badge <?php echo extension_loaded('curl') ? 'bg-success' : 'bg-danger'; ?>">
                                    cURL: <?php echo extension_loaded('curl') ? 'OK' : 'Missing'; ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <span class="badge <?php echo extension_loaded('gd') ? 'bg-success' : 'bg-danger'; ?>">
                                    GD: <?php echo extension_loaded('gd') ? 'OK' : 'Missing'; ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <span class="badge <?php echo extension_loaded('mbstring') ? 'bg-success' : 'bg-danger'; ?>">
                                    mbstring: <?php echo extension_loaded('mbstring') ? 'OK' : 'Missing'; ?>
                                </span>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-warning">
                            <strong>Security Notice:</strong> 
                            <p class="mb-0">Delete this file (test-connection.php) after successful testing for security reasons!</p>
                        </div>
                        
                        <div class="text-center">
                            <a href="index.php" class="btn btn-primary">Go to Homepage</a>
                            <a href="admin/dashboard.php" class="btn btn-secondary">Admin Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
</body>
</html>
