# SparkOn - Deployment Guide for Web Hosting

## 📋 Pre-Deployment Checklist

### 1. Web Hosting Requirements
- **PHP**: Version 8.0 or higher
- **MySQL**: Version 5.7 or higher
- **Web Server**: Apache or Nginx
- **SSL Certificate**: Required for Paystack payments
- **PHP Extensions**: PDO, cURL, GD, mbstring

### 2. Files to Upload via FileZilla

Upload the entire `sparkon` folder contents to your web hosting directory (usually `public_html` or `www`):

```
sparkon/
├── admin/
├── api/
├── assets/
├── auth/
├── config/
├── database/
├── uploads/ (create this folder with 755 permissions)
├── index.php
├── dashboard.php
├── order.php
├── payment.php
├── portfolio.php
├── profile.php
├── faq.php
├── order-tracking.php
├── orders.php
└── other PHP files...
```

### 3. Database Setup

#### Step 1: Create Database
1. Log into your hosting control panel (cPanel, Plesk, etc.)
2. Go to MySQL Databases
3. Create a new database named `sparkon` (or your preferred name)
4. Create a database user and assign it to the database
5. Note down: database name, username, password, and host

#### Step 2: Import Database
1. Go to phpMyAdmin in your hosting control panel
2. Select your database
3. Click "Import"
4. Upload the `database/sparkon.sql` file
5. Click "Go" to import

### 4. Configuration Updates

#### Update Database Configuration
Edit `config/database.php` with your hosting database details:

```php
private $host = 'your_hosting_db_host';        // Usually 'localhost'
private $db_name = 'your_database_name';       // Your created database name
private $username = 'your_db_username';        // Your database username
private $password = 'your_db_password';        // Your database password
```

#### Update Site Configuration
Edit `config/config.php`:

```php
// Update these with your domain
define('SITE_URL', 'https://yourdomain.com');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', 'your-phone-number');
```

#### Paystack Configuration (for payments)
```php
// Replace with your live Paystack keys for production
define('PAYSTACK_PUBLIC_KEY', 'pk_live_your_live_public_key');
define('PAYSTACK_SECRET_KEY', 'sk_live_your_live_secret_key');
```

### 5. FileZilla Upload Process

#### Step 1: Connect to Your Server
1. Open FileZilla
2. Enter your hosting details:
   - **Host**: Your domain or server IP
   - **Username**: Your hosting username
   - **Password**: Your hosting password
   - **Port**: 21 (FTP) or 22 (SFTP)

#### Step 2: Upload Files
1. Navigate to your local `sparkon` folder in the left panel
2. Navigate to your hosting directory (`public_html` or `www`) in the right panel
3. Select all files and folders in the `sparkon` directory
4. Right-click and select "Upload"
5. Wait for all files to upload

#### Step 3: Set Permissions
After upload, set these folder permissions via FileZilla:
- Right-click on `uploads/` folder → File Permissions → Set to 755
- Right-click on `assets/` folder → File Permissions → Set to 755

### 6. Post-Deployment Steps

#### Step 1: Test Database Connection
Visit: `https://yourdomain.com/test-connection.php` (create this file temporarily)

#### Step 2: Update Admin Credentials
1. Visit: `https://yourdomain.com/update_admin_credentials.php`
2. Change the default admin password
3. Delete this file after use for security

#### Step 3: Test Key Features
- [ ] Homepage loads correctly
- [ ] User registration works
- [ ] Login functionality works
- [ ] Admin panel accessible
- [ ] Payment system works (test mode first)
- [ ] File uploads work
- [ ] Contact forms work

### 7. Security Hardening

#### Step 1: Remove Test Files
Delete these files after deployment:
- `test-payment.php`
- `test-payment-vars.php`
- `simulate-payment.php`
- `update_admin_credentials.php` (after use)

#### Step 2: Create .htaccess for Security
Create `.htaccess` in root directory:

```apache
# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Hide sensitive files
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "database.php">
    Order allow,deny
    Deny from all
</Files>

# Clean URLs
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ $1.php [L,QSA]
```

### 8. SSL Certificate Setup

#### For cPanel Hosting:
1. Go to SSL/TLS in cPanel
2. Enable "Force HTTPS Redirect"
3. Install Let's Encrypt certificate (usually free)

#### Update config after SSL:
```php
define('SITE_URL', 'https://yourdomain.com'); // Note HTTPS
```

### 9. Email Configuration (Optional)

For contact forms to work, configure SMTP in `config/config.php`:

```php
// Uncomment and configure these lines
define('SMTP_HOST', 'mail.yourdomain.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
```

### 10. Backup Strategy

#### Automated Backups:
1. Set up automated database backups in your hosting control panel
2. Schedule weekly file backups
3. Keep local copies of important files

### 11. Monitoring & Maintenance

#### Regular Tasks:
- [ ] Monitor error logs
- [ ] Update admin password regularly
- [ ] Check for PHP updates
- [ ] Monitor payment transactions
- [ ] Backup database monthly

### 12. Troubleshooting Common Issues

#### Database Connection Errors:
- Verify database credentials
- Check if database server is running
- Ensure database user has proper permissions

#### File Upload Issues:
- Check folder permissions (755 for directories, 644 for files)
- Verify PHP upload limits in hosting control panel
- Ensure `uploads/` directory exists

#### Payment Issues:
- Switch to live Paystack keys for production
- Ensure SSL is properly configured
- Test with small amounts first

### 13. Performance Optimization

#### Enable Compression:
Add to `.htaccess`:
```apache
# Enable Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

## 🚀 Quick Deployment Summary

1. **Prepare**: Update config files with production settings
2. **Upload**: Use FileZilla to upload all files to hosting
3. **Database**: Create database and import SQL file
4. **Configure**: Update database and site settings
5. **Secure**: Set permissions and remove test files
6. **Test**: Verify all functionality works
7. **SSL**: Enable HTTPS and update configurations
8. **Monitor**: Set up backups and monitoring

## 📞 Support

If you encounter issues during deployment:
- Check your hosting provider's documentation
- Contact hosting support for server-specific issues
- Verify all configuration settings match your hosting environment

---

**Your SparkOn website should now be live and ready for business! 🎉**
