<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$db = getDB();

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    $order_id = (int)$_POST['order_id'];
    $new_status = sanitize($_POST['status']);
    $admin_notes = sanitize($_POST['admin_notes']);
    
    try {
        $stmt = $db->prepare("UPDATE orders SET status = ?, admin_notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$new_status, $admin_notes, $order_id]);
        
        // Get order and user details for notification
        $order_stmt = $db->prepare("
            SELECT o.*, u.full_name, u.email 
            FROM orders o 
            JOIN users u ON o.user_id = u.id 
            WHERE o.id = ?
        ");
        $order_stmt->execute([$order_id]);
        $order = $order_stmt->fetch();
        
        if ($order) {
            // Send status update email
            $subject = "Order Status Update - #" . $order_id;
            $status_text = ucfirst(str_replace('_', ' ', $new_status));
            $message = "
                <h2>Order Status Update</h2>
                <p>Hi {$order['full_name']},</p>
                <p>Your order status has been updated:</p>
                
                <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                    <h3>Order Details</h3>
                    <p><strong>Order ID:</strong> #{$order_id}</p>
                    <p><strong>Project:</strong> {$order['project_title']}</p>
                    <p><strong>New Status:</strong> {$status_text}</p>
                    " . ($admin_notes ? "<p><strong>Notes:</strong> {$admin_notes}</p>" : "") . "
                </div>
                
                <p>You can track your order progress anytime: 
                   <a href='" . SITE_URL . "/order-tracking.php?order_id={$order_id}'>View Order</a></p>
                
                <p>Best regards,<br>The SparkOn Team</p>
            ";
            
            sendEmail($order['email'], $subject, $message);
        }
        
        $success = 'Order status updated successfully.';
    } catch (Exception $e) {
        $error = 'Failed to update order status.';
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query with filters
$where_conditions = ['1=1'];
$params = [];

if ($status_filter) {
    $where_conditions[] = 'o.status = ?';
    $params[] = $status_filter;
}

if ($search) {
    $where_conditions[] = '(o.project_title LIKE ? OR u.full_name LIKE ? OR u.email LIKE ?)';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get orders with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

$orders_stmt = $db->prepare("
    SELECT o.*, u.full_name, u.email, c.name as category_name 
    FROM orders o 
    LEFT JOIN users u ON o.user_id = u.id 
    LEFT JOIN categories c ON o.category_id = c.id 
    WHERE {$where_clause}
    ORDER BY o.created_at DESC 
    LIMIT {$per_page} OFFSET {$offset}
");
$orders_stmt->execute($params);
$orders = $orders_stmt->fetchAll();

// Get total count for pagination
$count_stmt = $db->prepare("SELECT COUNT(*) as total FROM orders o LEFT JOIN users u ON o.user_id = u.id WHERE {$where_clause}");
$count_stmt->execute($params);
$total_orders = $count_stmt->fetch()['total'];
$total_pages = ceil($total_orders / $per_page);

// Get order statistics
$stats_stmt = $db->prepare("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        SUM(CASE WHEN payment_status = 'paid' THEN budget ELSE 0 END) as total_revenue
    FROM orders
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders Management - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #740ae8 0%, #0eaaf6 100%);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(116, 10, 232, 0.05);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <span style="color: #0eaaf6;">Spark</span>On
                        </h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link active" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                        </a>
                        <a class="nav-link" href="categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="portfolio.php">
                            <i class="fas fa-briefcase me-2"></i>Portfolio
                        </a>
                        <a class="nav-link" href="testimonials.php">
                            <i class="fas fa-star me-2"></i>Testimonials
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Header -->
                <div class="admin-header p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-0">Orders Management</h2>
                            <small class="text-muted">Manage and track all customer orders</small>
                        </div>
                        <div>
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('M j, Y'); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-list fa-2x text-primary mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['total']; ?></h4>
                                <small class="text-muted">Total Orders</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['pending']; ?></h4>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-2x text-info mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['paid']; ?></h4>
                                <small class="text-muted">Paid</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-cogs fa-2x text-primary mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['in_progress']; ?></h4>
                                <small class="text-muted">In Progress</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['completed']; ?></h4>
                                <small class="text-muted">Completed</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-2 col-md-4 col-sm-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo formatCurrency($stats['total_revenue']); ?></h4>
                                <small class="text-muted">Revenue</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Search Orders</label>
                                <input type="text" class="form-control" name="search" 
                                       placeholder="Search by project, client name, or email..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Filter by Status</label>
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>Paid</option>
                                    <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                    <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                                <a href="orders.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                            <div class="col-md-2 d-flex align-items-end justify-content-end">
                                <small class="text-muted"><?php echo $total_orders; ?> orders found</small>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Orders Table -->
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-list me-2"></i>All Orders
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($orders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No orders found</h5>
                                <p class="text-muted">
                                    <?php if ($search || $status_filter): ?>
                                        Try adjusting your search criteria or filters.
                                    <?php else: ?>
                                        No orders have been placed yet.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Order ID</th>
                                            <th>Client</th>
                                            <th>Project</th>
                                            <th>Category</th>
                                            <th>Budget</th>
                                            <th>Status</th>
                                            <th>Payment</th>
                                            <th>Deadline</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                            <tr>
                                                <td class="fw-medium">#<?php echo $order['id']; ?></td>
                                                <td>
                                                    <div>
                                                        <div class="fw-medium"><?php echo htmlspecialchars($order['full_name']); ?></div>
                                                        <small class="text-muted"><?php echo $order['email']; ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <div class="fw-medium"><?php echo htmlspecialchars(substr($order['project_title'], 0, 30)) . (strlen($order['project_title']) > 30 ? '...' : ''); ?></div>
                                                        <small class="text-muted"><?php echo htmlspecialchars(substr($order['project_description'], 0, 50)) . '...'; ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($order['category_name']): ?>
                                                        <span class="badge bg-light text-dark"><?php echo $order['category_name']; ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="fw-medium"><?php echo formatCurrency($order['budget']); ?></td>
                                                <td>
                                                    <?php
                                                    $status_classes = [
                                                        'pending' => 'bg-warning',
                                                        'paid' => 'bg-info',
                                                        'in_progress' => 'bg-primary',
                                                        'completed' => 'bg-success',
                                                        'cancelled' => 'bg-danger'
                                                    ];
                                                    $status_class = $status_classes[$order['status']] ?? 'bg-secondary';
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $payment_classes = [
                                                        'pending' => 'bg-warning',
                                                        'paid' => 'bg-success',
                                                        'failed' => 'bg-danger'
                                                    ];
                                                    $payment_class = $payment_classes[$order['payment_status']] ?? 'bg-secondary';
                                                    ?>
                                                    <span class="badge <?php echo $payment_class; ?>">
                                                        <?php echo ucfirst($order['payment_status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?php echo date('M j, Y', strtotime($order['deadline'])); ?></small>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="viewOrder(<?php echo $order['id']; ?>)" title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success" onclick="updateStatus(<?php echo $order['id']; ?>)" title="Update Status">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <div class="card-footer bg-white">
                                    <nav aria-label="Orders pagination">
                                        <ul class="pagination justify-content-center mb-0">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                                        <i class="fas fa-chevron-left"></i>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($page < $total_pages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Order Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" name="order_id" id="modalOrderId">
                        
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status" id="modalStatus" required>
                                <option value="pending">Pending</option>
                                <option value="paid">Paid</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Admin Notes</label>
                            <textarea class="form-control" name="admin_notes" id="modalNotes" rows="3" 
                                      placeholder="Add notes about this status update..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function updateStatus(orderId) {
            document.getElementById('modalOrderId').value = orderId;
            const modal = new bootstrap.Modal(document.getElementById('statusModal'));
            modal.show();
        }
        
        function viewOrder(orderId) {
            window.open(`../order-tracking.php?order_id=${orderId}`, '_blank');
        }
    </script>
</body>
</html>