<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

$message = '';
$error = '';

// Get order ID from URL
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

if ($order_id) {
    $db = getDB();
    
    // Get order details
    $stmt = $db->prepare("
        SELECT o.*, u.full_name, u.email 
        FROM orders o 
        JOIN users u ON o.user_id = u.id 
        WHERE o.id = ? AND o.user_id = ?
    ");
    $stmt->execute([$order_id, $_SESSION['user_id']]);
    $order = $stmt->fetch();
    
    if (!$order) {
        $error = 'Order not found or does not belong to you.';
    } else if ($order['payment_status'] === 'paid') {
        $message = 'This order is already marked as paid.';
    }
}

// Handle simulation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simulate'])) {
    $test_reference = 'TEST_' . $order_id . '_' . time();
    
    // Simulate successful payment by directly updating the database
    $db = getDB();
    $stmt = $db->prepare("
        UPDATE orders 
        SET payment_status = 'paid', 
            payment_reference = ?, 
            status = 'paid',
            updated_at = CURRENT_TIMESTAMP 
        WHERE id = ? AND user_id = ?
    ");
    
    if ($stmt->execute([$test_reference, $order_id, $_SESSION['user_id']])) {
        $message = 'Payment simulation successful! Order has been marked as paid.';
        
        // Redirect to order tracking after 3 seconds
        header("refresh:3;url=order-tracking.php?order_id=$order_id&payment=success");
    } else {
        $error = 'Failed to simulate payment. Database update failed.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Simulation - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-flask me-2"></i>Payment Simulation (Testing Only)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This is a testing tool that simulates successful payment without actually processing any payment. Use only for development/testing purposes.
                        </div>
                        
                        <?php if ($order && $order['payment_status'] !== 'paid'): ?>
                            <div class="mb-4">
                                <h6 class="fw-bold">Order Details:</h6>
                                <p><strong>Order ID:</strong> #<?php echo $order['id']; ?></p>
                                <p><strong>Project:</strong> <?php echo htmlspecialchars($order['project_title']); ?></p>
                                <p><strong>Budget:</strong> <?php echo formatCurrency($order['budget']); ?></p>
                                <p><strong>Current Status:</strong> 
                                    <span class="badge bg-warning"><?php echo ucfirst($order['payment_status']); ?></span>
                                </p>
                            </div>
                            
                            <form method="POST">
                                <input type="hidden" name="simulate" value="1">
                                <button type="submit" class="btn btn-warning w-100 mb-3">
                                    <i class="fas fa-flask me-2"></i>Simulate Successful Payment
                                </button>
                            </form>
                        <?php elseif ($order): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                This order is already marked as paid.
                            </div>
                        <?php else: ?>
                            <div class="mb-4">
                                <label for="order_id" class="form-label">Enter Order ID to Simulate Payment:</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="order_id" placeholder="Order ID">
                                    <button class="btn btn-primary" onclick="loadOrder()">Load Order</button>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div class="text-center">
                            <a href="dashboard.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <?php if ($order_id): ?>
                                <a href="payment.php?order_id=<?php echo $order_id; ?>" class="btn btn-success">
                                    <i class="fas fa-credit-card me-2"></i>Real Payment
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="fw-bold">How to Use:</h6>
                        <ol class="text-muted">
                            <li>This tool bypasses Paystack and directly marks an order as paid</li>
                            <li>Use this to test the post-payment flow without making actual payments</li>
                            <li>After simulation, you'll be redirected to the order tracking page</li>
                            <li>Remove this file from production environments</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function loadOrder() {
            const orderId = document.getElementById('order_id').value;
            if (orderId) {
                window.location.href = `simulate-payment.php?order_id=${orderId}`;
            }
        }
    </script>
</body>
</html>