<?php
/**
 * Admin Credentials Update Script
 * Updates admin login credentials to sparkon_admin / selables_GH
 */

require_once 'config/config.php';

// New admin credentials
$admin_email = '<EMAIL>';
$admin_password = 'selables_GH';
$admin_name = 'SparkOn Administrator';

// Hash the password
$hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

try {
    $db = getDB();
    
    // Check if admin user already exists
    $check_stmt = $db->prepare("SELECT id FROM users WHERE role = 'admin'");
    $check_stmt->execute();
    $existing_admin = $check_stmt->fetch();
    
    if ($existing_admin) {
        // Update existing admin user
        $update_stmt = $db->prepare("
            UPDATE users 
            SET full_name = ?, email = ?, password = ? 
            WHERE role = 'admin'
        ");
        $update_stmt->execute([$admin_name, $admin_email, $hashed_password]);
        echo "<h2>✅ Admin Credentials Updated Successfully!</h2>\n";
        echo "<p>Existing admin user has been updated.</p>\n";
    } else {
        // Create new admin user
        $insert_stmt = $db->prepare("
            INSERT INTO users (full_name, email, password, role) 
            VALUES (?, ?, ?, 'admin')
        ");
        $insert_stmt->execute([$admin_name, $admin_email, $hashed_password]);
        echo "<h2>✅ Admin User Created Successfully!</h2>\n";
        echo "<p>New admin user has been created.</p>\n";
    }
    
    echo "<div class='credentials'>\n";
    echo "<h3>🔐 New Admin Login Credentials:</h3>\n";
    echo "<p><strong>Email/Username:</strong> <EMAIL></p>\n";
    echo "<p><strong>Password:</strong> selables_GH</p>\n";
    echo "<p><strong>Login URL:</strong> <a href='auth/login.php'>auth/login.php</a></p>\n";
    echo "</div>\n";
    
    echo "<div class='info'>\n";
    echo "<h3>📝 Important Notes:</h3>\n";
    echo "<ul>\n";
    echo "<li>The password has been securely hashed using PHP's password_hash() function</li>\n";
    echo "<li>You can now login with these credentials</li>\n";
    echo "<li>Make sure to delete this script after running it for security</li>\n";
    echo "<li>The admin panel will be accessible after login</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // Update the SQL file with the correct hash
    $sql_content = file_get_contents('database/sparkon.sql');
    $sql_content = str_replace(
        '$2y$10$YourHashedPasswordHere',
        $hashed_password,
        $sql_content
    );
    file_put_contents('database/sparkon.sql', $sql_content);
    echo "<p>✅ Database SQL file updated with new credentials.</p>\n";
    
} catch (Exception $e) {
    echo "<h2>❌ Error Updating Admin Credentials</h2>\n";
    echo "<p>Error: " . $e->getMessage() . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f8f9fa;
}

h2 {
    color: #740ae8;
    border-bottom: 2px solid #0eaaf6;
    padding-bottom: 10px;
}

h3 {
    color: #0eaaf6;
}

.credentials {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border-left: 4px solid #28a745;
}

.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border-left: 4px solid #17a2b8;
}

.credentials p {
    margin: 10px 0;
    font-size: 16px;
}

.credentials strong {
    color: #155724;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 8px 0;
}

a {
    color: #740ae8;
    text-decoration: none;
}

a:hover {
    color: #0eaaf6;
    text-decoration: underline;
}
</style>