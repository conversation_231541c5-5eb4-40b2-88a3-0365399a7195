<?php
require_once 'config/config.php';

// Test script to check payment variables
echo "<h1>Payment Variables Test</h1>";

// Check if user is logged in (simulate for testing)
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Simulate logged in user
    $_SESSION['user_name'] = 'Test User';
}

// Get order ID from URL or use test value
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 1;

echo "<p>Order ID: $order_id</p>";

try {
    $db = getDB();
    echo "<p>Database connection: SUCCESS</p>";
    
    // Get order details
    $stmt = $db->prepare("
        SELECT o.*, c.name as category_name, u.full_name, u.email 
        FROM orders o 
        LEFT JOIN categories c ON o.category_id = c.id 
        LEFT JOIN users u ON o.user_id = u.id 
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if ($order) {
        echo "<p>Order found: SUCCESS</p>";
        echo "<p>Project Title: " . htmlspecialchars($order['project_title']) . "</p>";
        echo "<p>Budget: " . $order['budget'] . "</p>";
        echo "<p>Email: " . $order['email'] . "</p>";
        
        // Calculate fees
        $platform_fee = $order['budget'] * 0.05;
        $paystack_fee = ($order['budget'] + $platform_fee) * 0.015;
        $total_amount = $order['budget'] + $platform_fee + $paystack_fee;
        
        echo "<p>Platform Fee: $platform_fee</p>";
        echo "<p>Paystack Fee: $paystack_fee</p>";
        echo "<p>Total Amount: $total_amount</p>";
        echo "<p>Amount in Pesewas: " . (int)($total_amount * 100) . "</p>";
        
        // Generate payment reference
        $payment_reference = 'SPARK_' . $order_id . '_' . time();
        echo "<p>Payment Reference: $payment_reference</p>";
        
    } else {
        echo "<p>Order NOT found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

echo "<h2>Configuration Check</h2>";
echo "<p>PAYSTACK_PUBLIC_KEY: " . (PAYSTACK_PUBLIC_KEY ? 'SET' : 'NOT SET') . "</p>";
echo "<p>PAYSTACK_SECRET_KEY: " . (PAYSTACK_SECRET_KEY ? 'SET' : 'NOT SET') . "</p>";
echo "<p>SITE_NAME: " . SITE_NAME . "</p>";

// Test JavaScript variables
if (isset($order)) {
?>
<h2>JavaScript Test</h2>
<script>
console.log('Order ID:', <?php echo $order_id; ?>);
console.log('Total Amount:', <?php echo (int)($total_amount * 100); ?>);
console.log('Email:', '<?php echo htmlspecialchars($order['email']); ?>');
console.log('Reference:', '<?php echo $payment_reference; ?>');
console.log('Project Title:', '<?php echo addslashes(htmlspecialchars($order['project_title'])); ?>');
</script>
<?php
}
?>