<?php
/**
 * Main Configuration File
 * SparkOn - Company Portfolio Website
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

//Developer
define('DEVELOPER', 'SparkOn Web Team');

// Site Configuration
define('SITE_NAME', 'SparkOn');
define('SITE_URL', 'http://localhost/sparkon');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+*********** 113');

// Paystack Configuration
define('PAYSTACK_PUBLIC_KEY', 'pk_test_b802fc6a9eb929be5d77da0293b97327bf2d71d0');
define('PAYSTACK_SECRET_KEY', 'sk_test_e0089b8fdb31f08b3e2dbe04defccc67d02e85e3');

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

/* Email Configuration (for notifications)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password'); */

// Include database configuration
require_once 'database.php';

/**
 * Utility Functions
 */

// Sanitize input data
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// Redirect function
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// Generate CSRF token
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Verify CSRF token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Format currency
function formatCurrency($amount) {
    return '₵' . number_format($amount, 2);
}

// Time ago function
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    
    return floor($time/31536000) . ' years ago';
}

// Generate unique filename
function generateUniqueFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    return uniqid() . '_' . time() . '.' . $extension;
}

// Send email notification (basic implementation)
function sendEmail($to, $subject, $message) {
    $headers = "From: " . SITE_EMAIL . "\r\n";
    $headers .= "Reply-To: " . SITE_EMAIL . "\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($to, $subject, $message, $headers);
}
?>