<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$db = getDB();

// Get dashboard statistics
$stats = [];

// Total orders
$stmt = $db->prepare("SELECT COUNT(*) as count FROM orders");
$stmt->execute();
$stats['total_orders'] = $stmt->fetch()['count'];

// Pending orders
$stmt = $db->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
$stmt->execute();
$stats['pending_orders'] = $stmt->fetch()['count'];

// Active orders
$stmt = $db->prepare("SELECT COUNT(*) as count FROM orders WHERE status IN ('paid', 'in_progress')");
$stmt->execute();
$stats['active_orders'] = $stmt->fetch()['count'];

// Completed orders
$stmt = $db->prepare("SELECT COUNT(*) as count FROM orders WHERE status = 'completed'");
$stmt->execute();
$stats['completed_orders'] = $stmt->fetch()['count'];

// Total users
$stmt = $db->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'client'");
$stmt->execute();
$stats['total_users'] = $stmt->fetch()['count'];

// Total revenue
$stmt = $db->prepare("SELECT SUM(budget) as total FROM orders WHERE payment_status = 'paid'");
$stmt->execute();
$stats['total_revenue'] = $stmt->fetch()['total'] ?? 0;

// New contacts
$stmt = $db->prepare("SELECT COUNT(*) as count FROM contacts WHERE status = 'new'");
$stmt->execute();
$stats['new_contacts'] = $stmt->fetch()['count'];

// Recent orders
$recent_orders_stmt = $db->prepare("
    SELECT o.*, u.full_name, u.email, c.name as category_name 
    FROM orders o 
    LEFT JOIN users u ON o.user_id = u.id 
    LEFT JOIN categories c ON o.category_id = c.id 
    ORDER BY o.created_at DESC 
    LIMIT 10
");
$recent_orders_stmt->execute();
$recent_orders = $recent_orders_stmt->fetchAll();

// Recent contacts
$recent_contacts_stmt = $db->prepare("
    SELECT * FROM contacts 
    ORDER BY created_at DESC 
    LIMIT 5
");
$recent_contacts_stmt->execute();
$recent_contacts = $recent_contacts_stmt->fetchAll();

// Monthly revenue data for chart
$monthly_revenue_stmt = $db->prepare("
    SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        SUM(budget) as revenue,
        COUNT(*) as orders
    FROM orders 
    WHERE payment_status = 'paid' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month ASC
");
$monthly_revenue_stmt->execute();
$monthly_data = $monthly_revenue_stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #740ae8 0%, #0eaaf6 100%);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .stat-card {
            border: none;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(116, 10, 232, 0.05);
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <span style="color: #0eaaf6;">Spark</span>On
                        </h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                            <?php if ($stats['pending_orders'] > 0): ?>
                                <span class="badge bg-warning text-dark ms-auto"><?php echo $stats['pending_orders']; ?></span>
                            <?php endif; ?>
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                            <?php if ($stats['new_contacts'] > 0): ?>
                                <span class="badge bg-warning text-dark ms-auto"><?php echo $stats['new_contacts']; ?></span>
                            <?php endif; ?>
                        </a>
                        <a class="nav-link" href="categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="portfolio.php">
                            <i class="fas fa-briefcase me-2"></i>Portfolio
                        </a>
                        <a class="nav-link" href="testimonials.php">
                            <i class="fas fa-star me-2"></i>Testimonials
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Header -->
                <div class="admin-header p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-0">Dashboard</h2>
                            <small class="text-muted">Welcome back, <?php echo $_SESSION['user_name']; ?></small>
                        </div>
                        <div>
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('M j, Y'); ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="card stat-card shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-primary bg-opacity-10 text-primary me-3">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div>
                                        <h3 class="fw-bold mb-0"><?php echo $stats['total_orders']; ?></h3>
                                        <p class="text-muted mb-0">Total Orders</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card stat-card shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-warning bg-opacity-10 text-warning me-3">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <h3 class="fw-bold mb-0"><?php echo $stats['pending_orders']; ?></h3>
                                        <p class="text-muted mb-0">Pending Orders</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card stat-card shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-success bg-opacity-10 text-success me-3">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <h3 class="fw-bold mb-0"><?php echo $stats['total_users']; ?></h3>
                                        <p class="text-muted mb-0">Total Users</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card stat-card shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="stat-icon bg-info bg-opacity-10 text-info me-3">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div>
                                        <h3 class="fw-bold mb-0"><?php echo formatCurrency($stats['total_revenue']); ?></h3>
                                        <p class="text-muted mb-0">Total Revenue</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Recent Activity -->
                <div class="row g-4 mb-4">
                    <!-- Revenue Chart -->
                    <div class="col-lg-8">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">Monthly Revenue</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="revenueChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="col-lg-4">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">Quick Stats</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Active Orders</span>
                                    <span class="fw-bold text-primary"><?php echo $stats['active_orders']; ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>Completed Orders</span>
                                    <span class="fw-bold text-success"><?php echo $stats['completed_orders']; ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span>New Contacts</span>
                                    <span class="fw-bold text-warning"><?php echo $stats['new_contacts']; ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Success Rate</span>
                                    <span class="fw-bold text-info">
                                        <?php 
                                        $success_rate = $stats['total_orders'] > 0 ? 
                                            round(($stats['completed_orders'] / $stats['total_orders']) * 100) : 0;
                                        echo $success_rate . '%';
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Orders and Contacts -->
                <div class="row g-4">
                    <!-- Recent Orders -->
                    <div class="col-lg-8">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                                <h5 class="fw-bold mb-0">Recent Orders</h5>
                                <a href="orders.php" class="btn btn-outline-success btn-sm">View All</a>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Order ID</th>
                                                <th>Client</th>
                                                <th>Project</th>
                                                <th>Budget</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_orders as $order): ?>
                                                <tr>
                                                    <td class="fw-medium">#<?php echo $order['id']; ?></td>
                                                    <td>
                                                        <div>
                                                            <div class="fw-medium"><?php echo htmlspecialchars($order['full_name']); ?></div>
                                                            <small class="text-muted"><?php echo $order['email']; ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <div class="fw-medium"><?php echo htmlspecialchars(substr($order['project_title'], 0, 30)) . (strlen($order['project_title']) > 30 ? '...' : ''); ?></div>
                                                            <?php if ($order['category_name']): ?>
                                                                <small class="text-muted"><?php echo $order['category_name']; ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td class="fw-medium"><?php echo formatCurrency($order['budget']); ?></td>
                                                    <td>
                                                        <?php
                                                        $status_classes = [
                                                            'pending' => 'bg-warning',
                                                            'paid' => 'bg-info',
                                                            'in_progress' => 'bg-primary',
                                                            'completed' => 'bg-success',
                                                            'cancelled' => 'bg-danger'
                                                        ];
                                                        $status_class = $status_classes[$order['status']] ?? 'bg-secondary';
                                                        ?>
                                                        <span class="badge <?php echo $status_class; ?>">
                                                            <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small><?php echo date('M j, Y', strtotime($order['created_at'])); ?></small>
                                                    </td>
                                                    <td>
                                                        <a href="order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-primary btn-sm">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Contacts -->
                    <div class="col-lg-4">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                                <h5 class="fw-bold mb-0">Recent Contacts</h5>
                                <a href="contacts.php" class="btn btn-outline-success btn-sm">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_contacts)): ?>
                                    <div class="text-center text-muted py-3">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <p class="mb-0">No recent contacts</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($recent_contacts as $contact): ?>
                                        <div class="d-flex align-items-start mb-3 pb-3 border-bottom">
                                            <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                                                <i class="fas fa-envelope text-success"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="fw-bold mb-1"><?php echo htmlspecialchars($contact['name']); ?></h6>
                                                <p class="text-muted mb-1 small"><?php echo htmlspecialchars($contact['subject']); ?></p>
                                                <small class="text-muted"><?php echo timeAgo($contact['created_at']); ?></small>
                                            </div>
                                            <?php if ($contact['status'] === 'new'): ?>
                                                <span class="badge bg-warning">New</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Revenue Chart
        const ctx = document.getElementById('revenueChart').getContext('2d');
        const monthlyData = <?php echo json_encode($monthly_data); ?>;
        
        const labels = monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        });
        
        const revenueData = monthlyData.map(item => parseFloat(item.revenue) || 0);
        const orderData = monthlyData.map(item => parseInt(item.orders) || 0);
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenue (₵)',
                    data: revenueData,
                    borderColor: '#740ae8',
                    backgroundColor: 'rgba(116, 10, 232, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Orders',
                    data: orderData,
                    borderColor: '#0eaaf6',
                    backgroundColor: 'rgba(14, 170, 246, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        ticks: {
                            callback: function(value) {
                                return '₵' + value.toLocaleString();
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    </script>
</body>
</html>