<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

// Redirect admin to admin dashboard
if (isAdmin()) {
    redirect('admin/dashboard.php');
}

$db = getDB();

// Get user's orders statistics
$stats_stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as active_orders,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
        SUM(CASE WHEN payment_status = 'paid' THEN budget ELSE 0 END) as total_spent
    FROM orders 
    WHERE user_id = ?
");
$stats_stmt->execute([$_SESSION['user_id']]);
$stats = $stats_stmt->fetch();

// Get recent orders
$orders_stmt = $db->prepare("
    SELECT o.*, c.name as category_name 
    FROM orders o 
    LEFT JOIN categories c ON o.category_id = c.id 
    WHERE o.user_id = ? 
    ORDER BY o.created_at DESC 
    LIMIT 5
");
$orders_stmt->execute([$_SESSION['user_id']]);
$recent_orders = $orders_stmt->fetchAll();

// Get user profile
$user_stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$user_stmt->execute([$_SESSION['user_id']]);
$user = $user_stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <img src="assets/image/logo.png" height="50px" alt="">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">My Orders</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="order.php">New Project</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="orders.php"><i class="fas fa-list"></i> My Orders</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="fw-bold mb-1">Welcome back, <?php echo explode(' ', $_SESSION['user_name'])[0]; ?>!</h1>
                        <p class="text-muted mb-0">Here's what's happening with your projects</p>
                    </div>
                    <div>
                        <a href="order.php" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>New Project
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-5">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-project-diagram fa-lg text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="fw-bold mb-0"><?php echo $stats['total_orders']; ?></h3>
                                <p class="text-muted mb-0">Total Projects</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-clock fa-lg text-warning"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="fw-bold mb-0"><?php echo $stats['active_orders']; ?></h3>
                                <p class="text-muted mb-0">Active Projects</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-check-circle fa-lg text-success"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="fw-bold mb-0"><?php echo $stats['completed_orders']; ?></h3>
                                <p class="text-muted mb-0">Completed</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                    <i class="fas fa-dollar-sign fa-lg text-info"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h3 class="fw-bold mb-0"><?php echo formatCurrency($stats['total_spent'] ?? 0); ?></h3>
                                <p class="text-muted mb-0">Total Invested</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Recent Orders -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0 pb-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="fw-bold mb-0">Recent Projects</h5>
                            <a href="orders.php" class="btn btn-outline-success btn-sm">View All</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_orders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No projects yet</h6>
                                <p class="text-muted mb-3">Start your first project with us!</p>
                                <a href="order.php" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Submit Project Request
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Project</th>
                                            <th>Category</th>
                                            <th>Budget</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars(substr($order['project_title'], 0, 30)) . (strlen($order['project_title']) > 30 ? '...' : ''); ?></h6>
                                                        <small class="text-muted">Order #<?php echo $order['id']; ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($order['category_name']): ?>
                                                        <span class="badge bg-light text-dark"><?php echo $order['category_name']; ?></span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="fw-medium"><?php echo formatCurrency($order['budget']); ?></td>
                                                <td>
                                                    <?php
                                                    $status_classes = [
                                                        'pending' => 'bg-warning',
                                                        'paid' => 'bg-info',
                                                        'in_progress' => 'bg-primary',
                                                        'completed' => 'bg-success',
                                                        'cancelled' => 'bg-danger'
                                                    ];
                                                    $status_class = $status_classes[$order['status']] ?? 'bg-secondary';
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <a href="order-tracking.php?order_id=<?php echo $order['id']; ?>" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Profile -->
            <div class="col-lg-4">
                <!-- Profile Card -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                <i class="fas fa-user fa-2x text-success"></i>
                            </div>
                        </div>
                        <h5 class="fw-bold mb-1"><?php echo $user['full_name']; ?></h5>
                        <p class="text-muted mb-3"><?php echo $user['email']; ?></p>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h6 class="fw-bold mb-0"><?php echo $stats['total_orders']; ?></h6>
                                    <small class="text-muted">Projects</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h6 class="fw-bold mb-0"><?php echo date('M Y', strtotime($user['created_at'])); ?></h6>
                                <small class="text-muted">Member Since</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="profile.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-edit me-1"></i>Edit Profile
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-0">
                        <h6 class="fw-bold mb-0">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="order.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>New Project Request
                            </a>
                            <a href="orders.php" class="btn btn-outline-primary">
                                <i class="fas fa-list me-2"></i>View All Orders
                            </a>
                            <a href="#" class="btn btn-outline-info" onclick="toggleChat()">
                                <i class="fas fa-comments me-2"></i>Chat Support
                            </a>
                            <a href="faq.php" class="btn btn-outline-secondary">
                                <i class="fas fa-question-circle me-2"></i>Help & FAQ
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Tips Card -->
                <div class="card border-0 shadow-sm mt-4 bg-light">
                    <div class="card-body">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-lightbulb text-warning me-2"></i>Pro Tip
                        </h6>
                        <p class="mb-0 small">
                            Provide detailed project descriptions and reference materials to get the best results. 
                            Our team loves working with clear requirements!
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Bot -->
    <div class="chat-widget" id="chatWidget">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </div>
        
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">SparkOn Assistant</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="bot-message">
                    <p>Hi <?php echo explode(' ', $_SESSION['user_name'])[0]; ?>! How can I help you today?</p>
                </div>
            </div>
            <div class="chat-input">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                    <button class="btn btn-success" onclick="sendChatMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
</body>
</html>