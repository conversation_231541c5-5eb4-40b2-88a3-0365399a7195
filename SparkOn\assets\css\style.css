/* SparkOn - Custom CSS Styles */
/* Fiverr-inspired design with modern touches */

:root {
    --primary-color: #740ae8;
    --primary-dark: #5c08b8;
    --secondary-color: #0eaaf6;
    --secondary-dark: #0b88c4;
    --dark-color: #404145;
    --light-gray: #f7f7f7;
    --border-color: #e4e5e7;
    --text-muted: #62646a;
    --white: #ffffff;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Macan', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
}

.lead {
    font-size: 1.1rem;
    font-weight: 400;
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-outline-success {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-success:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Navigation */
.navbar {
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--dark-color);
    margin: 0 0.5rem;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.min-vh-75 {
    min-height: 75vh;
}

.search-box {
    box-shadow: var(--shadow);
    max-width: 500px;
}

.search-box input {
    font-size: 1rem;
    padding: 0.75rem 1rem;
}

.search-box input:focus {
    outline: none;
    box-shadow: none;
}

/* Client Logos */
.client-logo {
    height: 40px;
    opacity: 0.6;
    transition: opacity 0.3s ease;
    filter: grayscale(100%);
}

.client-logo:hover {
    opacity: 1;
    filter: grayscale(0%);
}

/* Service Cards */
.service-card {
    background: var(--white);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: rgba(116, 10, 232, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Step Icons */
.step-icon {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.step-icon:hover {
    transform: scale(1.1);
}

/* Portfolio Cards */
.portfolio-card {
    background: var(--white);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.portfolio-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}

/* Testimonial Cards */
.testimonial-card {
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.testimonial-avatar {
    font-weight: 600;
    font-size: 1.2rem;
}

/* Contact Section */
.contact-icon {
    transition: all 0.3s ease;
}

.contact-item:hover .contact-icon {
    transform: scale(1.1);
}

/* Forms */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(29, 191, 115, 0.25);
}

/* Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.chat-toggle:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.chat-window {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 350px;
    height: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow-hover);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.chat-window.active {
    display: flex;
}

.chat-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 280px;
}

.bot-message, .user-message {
    margin-bottom: 1rem;
}

.bot-message p {
    background: var(--light-gray);
    padding: 0.75rem;
    border-radius: 10px 10px 10px 2px;
    margin: 0;
    font-size: 0.9rem;
}

.user-message p {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem;
    border-radius: 10px 10px 2px 10px;
    margin: 0;
    font-size: 0.9rem;
    text-align: right;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.chat-input input {
    border: none;
    outline: none;
    font-size: 0.9rem;
}

.chat-input input:focus {
    box-shadow: none;
}

/* Utility Classes */
.hover-shadow {
    transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: var(--shadow-hover);
}

.text-success {
    color: var(--primary-color) !important;
}

.bg-success {
    background-color: var(--primary-color) !important;
}

.border-success {
    border-color: var(--primary-color) !important;
}

/* Responsive Design */
/* Additional color utilities */
.text-secondary {
    color: var(--secondary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.border-secondary {
    border-color: var(--secondary-color) !important;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    color: white;
}

.btn-outline-secondary {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0;
        height: 700px;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-image{
        display: none;
    }
    
    .search-box {
        max-width: 100%;
    }
    
    .chat-window {
        width: 300px;
        height: 350px;
    }
    
    .service-card {
        text-align: center;
    }
    
    .portfolio-card {
        margin-bottom: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 1.75rem;
    }
    
    .chat-widget {
        bottom: 15px;
        right: 15px;
    }
    
    .chat-toggle {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .chat-window {
        width: 280px;
        height: 320px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Success/Error Messages */
.alert {
    border-radius: 4px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(116, 10, 232, 0.1);
    color: var(--primary-dark);
    border-left: 4px solid var(--primary-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Print Styles */
@media print {
    .navbar, .chat-widget, footer {
        display: none !important;
    }
    
    .hero-section {
        background: white !important;
        color: black !important;
    }
}