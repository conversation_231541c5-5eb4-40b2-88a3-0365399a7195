<?php
header('Content-Type: application/json');
require_once '../config/config.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $user_message = trim($input['message'] ?? '');
    $session_id = $input['session_id'] ?? '';
    
    if (empty($user_message)) {
        echo json_encode(['success' => false, 'message' => 'Message is required']);
        exit;
    }
    
    // Generate bot response based on user message
    $bot_response = generateBotResponse($user_message);
    
    // Save chat message to database
    $db = getDB();
    $stmt = $db->prepare("INSERT INTO chat_messages (session_id, user_message, bot_response) VALUES (?, ?, ?)");
    $stmt->execute([$session_id, $user_message, $bot_response]);
    
    echo json_encode([
        'success' => true,
        'response' => $bot_response
    ]);
    
} catch (Exception $e) {
    error_log("Chatbot error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Sorry, I encountered an error.']);
}

/**
 * Generate bot response based on user message
 */
function generateBotResponse($message) {
    $message = strtolower($message);
    
    // Greeting responses
    if (preg_match('/\b(hi|hello|hey|good morning|good afternoon|good evening)\b/', $message)) {
        $responses = [
            "Hello! Welcome to SparkOn. How can I help you today?",
            "Hi there! I'm here to assist you with any questions about our services.",
            "Hey! Thanks for visiting SparkOn. What can I help you with?"
        ];
        return $responses[array_rand($responses)];
    }
    
    // Services related
    if (preg_match('/\b(service|services|what do you do|what do you offer)\b/', $message)) {
        return "We offer a wide range of digital services including:\n\n• Web Development\n• Mobile App Development\n• UI/UX Design\n• Digital Marketing\n• Graphic Design\n• Content Writing\n• Data Analysis\n• Business Consulting\n\nWhich service interests you most?";
    }
    
    // Pricing related
    if (preg_match('/\b(price|pricing|cost|how much|budget)\b/', $message)) {
        return "Our pricing varies based on project complexity and requirements. Here's a general guide:\n\n• Simple websites: ₵500 - ₵2,000\n• E-commerce sites: ₵2,000 - ₵8,000\n• Mobile apps: ₵3,000 - ₵15,000\n• Design projects: ₵200 - ₵2,000\n\nFor an accurate quote, please submit a project request with your specific requirements.";
    }
    
    // Timeline related
    if (preg_match('/\b(time|timeline|how long|duration|when)\b/', $message)) {
        return "Project timelines depend on complexity:\n\n• Simple websites: 1-2 weeks\n• Complex websites: 3-6 weeks\n• Mobile apps: 6-12 weeks\n• Design projects: 3-7 days\n\nWe always provide detailed timelines during project consultation.";
    }
    
    // Process related
    if (preg_match('/\b(process|how it works|steps|procedure)\b/', $message)) {
        return "Our process is simple:\n\n1. **Submit Request** - Tell us about your project\n2. **Make Payment** - Secure payment through Paystack\n3. **We Work** - Our team brings your vision to life\n4. **Get Delivery** - Receive your completed project\n\nYou can track progress throughout the entire process!";
    }
    
    // Contact related
    if (preg_match('/\b(contact|phone|email|reach|talk)\b/', $message)) {
        return "You can reach us through:\n\n📧 Email: " . SITE_EMAIL . "\n📞 Phone: " . SITE_PHONE . "\n💬 Live Chat: Right here!\n📍 Address: 123 Business Street, Accra, Ghana\n\nWe typically respond within 2 hours during business hours.";
    }
    
    // Payment related
    if (preg_match('/\b(payment|pay|paystack|card|mobile money)\b/', $message)) {
        return "We accept secure payments through Paystack:\n\n• Credit/Debit Cards (Visa, Mastercard)\n• Mobile Money (MTN, Vodafone, AirtelTigo)\n• Bank Transfers\n\nAll payments are secured with industry-standard encryption. We also offer a 100% money-back guarantee if you're not satisfied.";
    }
    
    // Support related
    if (preg_match('/\b(support|help|problem|issue|stuck)\b/', $message)) {
        return "I'm here to help! For immediate assistance:\n\n• Continue chatting with me for quick questions\n• Email us at " . SITE_EMAIL . " for detailed inquiries\n• Call " . SITE_PHONE . " for urgent matters\n• Check our FAQ section for common questions\n\nWhat specific issue can I help you with?";
    }
    
    // Portfolio related
    if (preg_match('/\b(portfolio|work|examples|previous projects)\b/', $message)) {
        return "We've completed 500+ successful projects! You can view our featured work on the homepage or visit our full portfolio section.\n\nSome highlights:\n• E-commerce platforms\n• Mobile applications\n• Brand identity packages\n• Business analytics dashboards\n\nWould you like to see examples in a specific category?";
    }
    
    // Team related
    if (preg_match('/\b(team|who are you|about|company)\b/', $message)) {
        return "SparkOn is a team of 50+ passionate digital professionals with 5+ years of experience. We specialize in helping businesses thrive in the digital world.\n\n✅ 500+ Projects Completed\n✅ 98% Client Satisfaction\n✅ 5+ Years Experience\n✅ Expert Team\n\nWe're committed to delivering excellence in every project!";
    }
    
    // Thanks
    if (preg_match('/\b(thank|thanks|appreciate)\b/', $message)) {
        $responses = [
            "You're welcome! Is there anything else I can help you with?",
            "Happy to help! Feel free to ask if you have more questions.",
            "My pleasure! Let me know if you need anything else."
        ];
        return $responses[array_rand($responses)];
    }
    
    // Goodbye
    if (preg_match('/\b(bye|goodbye|see you|talk later)\b/', $message)) {
        $responses = [
            "Goodbye! Thanks for visiting SparkOn. Feel free to reach out anytime!",
            "See you later! Don't hesitate to contact us when you're ready to start your project.",
            "Take care! We're here whenever you need digital solutions."
        ];
        return $responses[array_rand($responses)];
    }
    
    // Default response for unmatched queries
    $default_responses = [
        "I'd be happy to help! Could you please be more specific about what you're looking for?",
        "That's an interesting question! For detailed information, you might want to contact our team directly at " . SITE_EMAIL . " or call " . SITE_PHONE . ".",
        "I want to make sure I give you the best answer. Could you rephrase your question or let me know which service you're interested in?",
        "Great question! Here are some topics I can help with:\n\n• Our services and pricing\n• Project timelines\n• Payment methods\n• Contact information\n• How our process works\n\nWhat would you like to know more about?"
    ];
    
    return $default_responses[array_rand($default_responses)];
}
?>