<?php
require_once 'config/config.php';

$db = getDB();

// Get filter parameters
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query with filters
$where_conditions = ['1=1'];
$params = [];

if ($category_filter) {
    $where_conditions[] = 'p.category_id = ?';
    $params[] = $category_filter;
}

if ($search) {
    $where_conditions[] = '(p.title LIKE ? OR p.description LIKE ?)';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get portfolio items with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

$portfolio_stmt = $db->prepare("
    SELECT p.*, c.name as category_name 
    FROM portfolio p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE {$where_clause}
    ORDER BY p.is_featured DESC, p.created_at DESC 
    LIMIT {$per_page} OFFSET {$offset}
");
$portfolio_stmt->execute($params);
$portfolio_items = $portfolio_stmt->fetchAll();

// Get total count for pagination
$count_stmt = $db->prepare("SELECT COUNT(*) as total FROM portfolio p WHERE {$where_clause}");
$count_stmt->execute($params);
$total_items = $count_stmt->fetch()['total'];
$total_pages = ceil($total_items / $per_page);

// Get categories for filter
$categories_stmt = $db->prepare("SELECT * FROM categories ORDER BY name");
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="portfolio.php">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="faq.php">FAQ</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                <li><a class="dropdown-item" href="orders.php"><i class="fas fa-list"></i> My Orders</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Sign In</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-success ms-2" href="auth/register.php">Join Now</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="fw-bold mb-3">Our Portfolio</h1>
                    <p class="lead text-muted mb-4">
                        Explore our collection of successful projects and see how we've helped businesses achieve their digital goals
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Content -->
    <section class="py-5">
        <div class="container">
            <!-- Filters -->
            <div class="row mb-5">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <form method="GET" class="row g-3 align-items-end">
                                <div class="col-md-4">
                                    <label class="form-label">Search Projects</label>
                                    <input type="text" class="form-control" name="search" 
                                           placeholder="Search by project name..." 
                                           value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Filter by Category</label>
                                    <select class="form-select" name="category">
                                        <option value="">All Categories</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>" 
                                                    <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                                <?php echo $category['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                    <a href="portfolio.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </a>
                                </div>
                                <div class="col-md-2 text-end">
                                    <small class="text-muted"><?php echo $total_items; ?> projects found</small>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Portfolio Grid -->
            <?php if (empty($portfolio_items)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No projects found</h5>
                    <p class="text-muted mb-3">
                        <?php if ($search || $category_filter): ?>
                            Try adjusting your search criteria or filters.
                        <?php else: ?>
                            We're working on adding more projects to our portfolio.
                        <?php endif; ?>
                    </p>
                    <?php if ($search || $category_filter): ?>
                        <a href="portfolio.php" class="btn btn-primary">View All Projects</a>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="row g-4 mb-5">
                    <?php foreach ($portfolio_items as $item): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="portfolio-card h-100 rounded-3 overflow-hidden shadow-sm">
                                <div class="portfolio-image position-relative">
                                    <?php if ($item['image']): ?>
                                        <img src="<?php echo htmlspecialchars($item['image']); ?>" 
                                             alt="<?php echo htmlspecialchars($item['title']); ?>" 
                                             class="img-fluid w-100" 
                                             style="height: 250px; object-fit: cover;"
                                             onerror="this.src='assets/images/portfolio-placeholder.svg'">
                                    <?php else: ?>
                                        <div class="d-flex align-items-center justify-content-center bg-light text-muted" style="height: 250px;">
                                            <i class="fas fa-image fa-3x"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($item['is_featured']): ?>
                                        <span class="position-absolute top-0 start-0 badge bg-warning m-2">
                                            <i class="fas fa-star me-1"></i>Featured
                                        </span>
                                    <?php endif; ?>
                                    
                                    <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center">
                                        <div class="text-center">
                                            <button class="btn btn-light btn-sm me-2" onclick="viewProject(<?php echo $item['id']; ?>)">
                                                <i class="fas fa-eye me-1"></i>View Details
                                            </button>
                                            <?php if ($item['project_url']): ?>
                                                <a href="<?php echo htmlspecialchars($item['project_url']); ?>" 
                                                   target="_blank" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-external-link-alt me-1"></i>Live Demo
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="fw-bold mb-0"><?php echo htmlspecialchars($item['title']); ?></h5>
                                        <?php if ($item['category_name']): ?>
                                            <span class="badge bg-primary"><?php echo $item['category_name']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <p class="text-muted mb-3">
                                        <?php echo htmlspecialchars(substr($item['description'], 0, 120)) . (strlen($item['description']) > 120 ? '...' : ''); ?>
                                    </p>
                                    
                                    <?php if ($item['technologies']): ?>
                                        <div class="d-flex flex-wrap gap-1 mb-3">
                                            <?php 
                                            $technologies = json_decode($item['technologies'], true);
                                            if ($technologies) {
                                                foreach (array_slice($technologies, 0, 4) as $tech): 
                                            ?>
                                                <span class="badge bg-light text-dark"><?php echo htmlspecialchars($tech); ?></span>
                                            <?php 
                                                endforeach;
                                                if (count($technologies) > 4) {
                                                    echo '<span class="badge bg-light text-dark">+' . (count($technologies) - 4) . ' more</span>';
                                                }
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo date('M Y', strtotime($item['created_at'])); ?>
                                        </small>
                                        <button class="btn btn-outline-primary btn-sm" onclick="viewProject(<?php echo $item['id']; ?>)">
                                            Learn More
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Portfolio pagination">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&category=<?php echo $category_filter; ?>&search=<?php echo urlencode($search); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&category=<?php echo $category_filter; ?>&search=<?php echo urlencode($search); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&category=<?php echo $category_filter; ?>&search=<?php echo urlencode($search); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold mb-3">Ready to Start Your Project?</h3>
                    <p class="text-muted mb-4">
                        Let's work together to create something amazing for your business. 
                        Our team is ready to bring your vision to life.
                    </p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="order.php" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Start Your Project
                        </a>
                        <a href="index.php#contact" class="btn btn-outline-primary">
                            <i class="fas fa-comments me-2"></i>Get in Touch
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">
                        <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
                    </h5>
                    <p class="text-muted mb-4">
                        Your trusted partner for digital transformation. We help businesses 
                        grow through innovative digital solutions.
                    </p>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="index.php#services" class="text-muted text-decoration-none">Services</a></li>
                        <li><a href="portfolio.php" class="text-muted text-decoration-none">Portfolio</a></li>
                        <li><a href="index.php#about" class="text-muted text-decoration-none">About</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="faq.php" class="text-muted text-decoration-none">FAQ</a></li>
                        <li><a href="index.php#contact" class="text-muted text-decoration-none">Contact</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Terms</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Privacy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <p class="text-muted mb-1">
                        <i class="fas fa-envelope me-2"></i><?php echo SITE_EMAIL; ?>
                    </p>
                    <p class="text-muted mb-1">
                        <i class="fas fa-phone me-2"></i><?php echo SITE_PHONE; ?>
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt me-2"></i>123 Business Street, Accra, Ghana
                    </p>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 SparkOn. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Made with <i class="fas fa-heart text-danger"></i> in Ghana</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Project Details Modal -->
    <div class="modal fade" id="projectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="projectModalTitle">Project Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="projectModalBody">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // View project details
        function viewProject(projectId) {
            const modal = new bootstrap.Modal(document.getElementById('projectModal'));
            const modalBody = document.getElementById('projectModalBody');
            const modalTitle = document.getElementById('projectModalTitle');
            
            // Show loading
            modalBody.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;
            
            modal.show();
            
            // Fetch project details (you can implement this API endpoint)
            fetch(`api/project-details.php?id=${projectId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        modalTitle.textContent = data.project.title;
                        modalBody.innerHTML = `
                            <div class="row g-4">
                                <div class="col-md-6">
                                    ${data.project.image ? 
                                        `<img src="${data.project.image}" 
                                             alt="${data.project.title}" 
                                             class="img-fluid rounded"
                                             onerror="this.src='assets/images/portfolio-placeholder.svg'">` :
                                        `<div class="d-flex align-items-center justify-content-center bg-light text-muted rounded" style="height: 200px;">
                                            <i class="fas fa-image fa-3x"></i>
                                        </div>`
                                    }
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Description</h6>
                                    <p class="text-muted">${data.project.description}</p>
                                    
                                    <h6 class="fw-bold">Category</h6>
                                    <span class="badge bg-primary mb-3">${data.project.category_name || 'Uncategorized'}</span>
                                    
                                    <h6 class="fw-bold">Technologies Used</h6>
                                    <div class="d-flex flex-wrap gap-1 mb-3">
                                        ${data.project.technologies ? JSON.parse(data.project.technologies).map(tech => 
                                            `<span class="badge bg-light text-dark">${tech}</span>`
                                        ).join('') : 'Not specified'}
                                    </div>
                                    
                                    ${data.project.project_url ? `
                                        <a href="${data.project.project_url}" target="_blank" class="btn btn-primary">
                                            <i class="fas fa-external-link-alt me-2"></i>View Live Project
                                        </a>
                                    ` : ''}
                                </div>
                            </div>
                        `;
                    } else {
                        modalBody.innerHTML = `
                            <div class="text-center py-4">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                                <p class="text-muted">Failed to load project details.</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    modalBody.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                            <p class="text-muted">Error loading project details.</p>
                        </div>
                    `;
                });
        }
    </script>
    
    <style>
        .portfolio-overlay {
            background: rgba(0, 0, 0, 0.7);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .portfolio-card:hover .portfolio-overlay {
            opacity: 1;
        }
        
        .portfolio-card {
            transition: transform 0.3s ease;
        }
        
        .portfolio-card:hover {
            transform: translateY(-5px);
        }
    </style>
</body>
</html>