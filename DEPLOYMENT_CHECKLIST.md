# SparkOn Deployment Checklist

## ✅ Pre-Deployment Tasks

### 1. Hosting Setup
- [ ] Web hosting account created
- [ ] Domain name configured
- [ ] SSL certificate installed
- [ ] PHP 8.0+ confirmed
- [ ] MySQL 5.7+ confirmed

### 2. Database Preparation
- [ ] Database created on hosting
- [ ] Database user created with full permissions
- [ ] Database credentials noted down
- [ ] `sparkon.sql` file ready for import

### 3. Configuration Updates
- [ ] Updated `config/config.php` with production domain
- [ ] Updated `config/database.php` with hosting credentials
- [ ] Updated Paystack keys to LIVE keys (for production payments)
- [ ] Updated contact email and phone number

## 📁 FileZilla Upload Process

### 4. File Upload
- [ ] Connected to hosting server via FileZilla
- [ ] Uploaded all files from `sparkon/` folder to `public_html/` or `www/`
- [ ] Verified all files uploaded successfully
- [ ] Set folder permissions: `uploads/` = 755, `assets/` = 755

### 5. Database Import
- [ ] Accessed phpMyAdmin from hosting control panel
- [ ] Selected the created database
- [ ] Imported `database/sparkon.sql` file
- [ ] Verified all tables imported correctly

## 🔧 Post-Deployment Testing

### 6. Basic Functionality
- [ ] Homepage loads without errors
- [ ] Navigation menu works
- [ ] CSS and JavaScript files loading
- [ ] Images displaying correctly

### 7. Database Connection
- [ ] Visited `yourdomain.com/test-connection.php`
- [ ] Database connection successful
- [ ] All tables found
- [ ] Admin user exists

### 8. User Features
- [ ] User registration works
- [ ] User login works
- [ ] Password reset works
- [ ] User dashboard accessible

### 9. Admin Features
- [ ] Admin login works (<EMAIL> / selables_GH)
- [ ] Admin dashboard loads
- [ ] Can view orders
- [ ] Can manage users
- [ ] Can update settings

### 10. Payment System
- [ ] Payment form loads
- [ ] Paystack integration works
- [ ] Test payment successful (use test card: ****************)
- [ ] Payment verification works

### 11. File Uploads
- [ ] Project file upload works
- [ ] Files saved to `uploads/` directory
- [ ] File permissions correct

### 12. Contact Forms
- [ ] Contact form submits successfully
- [ ] Email notifications work (if configured)
- [ ] Form data saved to database

## 🔒 Security Hardening

### 13. Remove Test Files
- [ ] Deleted `test-connection.php`
- [ ] Deleted `test-payment.php`
- [ ] Deleted `test-payment-vars.php`
- [ ] Deleted `simulate-payment.php`
- [ ] Deleted `config/database.production.php`

### 14. Security Configuration
- [ ] `.htaccess` file uploaded and working
- [ ] Sensitive directories protected
- [ ] HTTPS redirect enabled (uncomment in .htaccess)
- [ ] Changed default admin password

### 15. Performance Optimization
- [ ] Gzip compression enabled
- [ ] Browser caching configured
- [ ] Images optimized
- [ ] CSS/JS minified (optional)

## 🚀 Go Live

### 16. Final Steps
- [ ] Updated all internal links to use new domain
- [ ] Tested all major user flows
- [ ] Set up automated backups
- [ ] Configured error logging
- [ ] Submitted sitemap to Google

### 17. Monitoring Setup
- [ ] Google Analytics installed (optional)
- [ ] Error monitoring configured
- [ ] Uptime monitoring setup
- [ ] Regular backup schedule

## 📞 Support Information

### Default Admin Login
- **URL**: `yourdomain.com/admin/dashboard.php`
- **Email**: `<EMAIL>`
- **Password**: `selables_GH`
- **⚠️ CHANGE THIS PASSWORD IMMEDIATELY AFTER FIRST LOGIN**

### Test Payment Card (Paystack)
- **Card Number**: `****************`
- **Expiry**: Any future date
- **CVV**: Any 3 digits

### Important Files to Backup
- `config/config.php`
- `config/database.php`
- `uploads/` directory
- Database export

### Common Issues & Solutions

**Database Connection Failed:**
- Check credentials in `config/database.php`
- Ensure database exists
- Verify user permissions

**Files Not Loading:**
- Check file permissions
- Verify .htaccess is working
- Clear browser cache

**Payment Not Working:**
- Ensure SSL is configured
- Check Paystack keys
- Verify webhook URLs

**Email Not Sending:**
- Configure SMTP settings
- Check hosting email limits
- Verify sender email

## 🎉 Deployment Complete!

Once all items are checked, your SparkOn website is ready for business!

**Next Steps:**
1. Share the website with stakeholders
2. Start marketing and promotion
3. Monitor performance and user feedback
4. Plan regular maintenance and updates

---

**Need Help?**
- Check hosting provider documentation
- Contact hosting support for server issues
- Review error logs for troubleshooting
