<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$db = getDB();

// Handle contact actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $contact_id = (int)$_POST['contact_id'];
        
        try {
            switch ($action) {
                case 'mark_read':
                    $stmt = $db->prepare("UPDATE contacts SET status = 'read' WHERE id = ?");
                    $stmt->execute([$contact_id]);
                    $success = 'Contact marked as read.';
                    break;
                    
                case 'mark_replied':
                    $stmt = $db->prepare("UPDATE contacts SET status = 'replied' WHERE id = ?");
                    $stmt->execute([$contact_id]);
                    $success = 'Contact marked as replied.';
                    break;
                    
                case 'delete_contact':
                    $stmt = $db->prepare("DELETE FROM contacts WHERE id = ?");
                    $stmt->execute([$contact_id]);
                    $success = 'Contact deleted successfully.';
                    break;
                    
                case 'reply_contact':
                    $reply_message = sanitize($_POST['reply_message']);
                    
                    // Get contact details
                    $contact_stmt = $db->prepare("SELECT * FROM contacts WHERE id = ?");
                    $contact_stmt->execute([$contact_id]);
                    $contact = $contact_stmt->fetch();
                    
                    if ($contact) {
                        // Send reply email
                        $subject = "Re: " . $contact['subject'];
                        $message = "
                            <h2>Reply from SparkOn</h2>
                            <p>Hi {$contact['name']},</p>
                            <p>Thank you for contacting us. Here's our response to your inquiry:</p>
                            
                            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                                <h4>Your Original Message:</h4>
                                <p><strong>Subject:</strong> {$contact['subject']}</p>
                                <p>{$contact['message']}</p>
                            </div>
                            
                            <div style='background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                                <h4>Our Response:</h4>
                                <p>{$reply_message}</p>
                            </div>
                            
                            <p>If you have any further questions, please don't hesitate to contact us.</p>
                            <p>Best regards,<br>The SparkOn Team</p>
                        ";
                        
                        if (sendEmail($contact['email'], $subject, $message)) {
                            // Mark as replied
                            $stmt = $db->prepare("UPDATE contacts SET status = 'replied' WHERE id = ?");
                            $stmt->execute([$contact_id]);
                            $success = 'Reply sent successfully and contact marked as replied.';
                        } else {
                            $error = 'Failed to send reply email.';
                        }
                    }
                    break;
            }
        } catch (Exception $e) {
            $error = 'Failed to perform action: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query with filters
$where_conditions = ['1=1'];
$params = [];

if ($status_filter) {
    $where_conditions[] = 'status = ?';
    $params[] = $status_filter;
}

if ($search) {
    $where_conditions[] = '(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
}

$where_clause = implode(' AND ', $where_conditions);

// Get contacts with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

$contacts_stmt = $db->prepare("
    SELECT * FROM contacts 
    WHERE {$where_clause}
    ORDER BY created_at DESC 
    LIMIT {$per_page} OFFSET {$offset}
");
$contacts_stmt->execute($params);
$contacts = $contacts_stmt->fetchAll();

// Get total count for pagination
$count_stmt = $db->prepare("SELECT COUNT(*) as total FROM contacts WHERE {$where_clause}");
$count_stmt->execute($params);
$total_contacts = $count_stmt->fetch()['total'];
$total_pages = ceil($total_contacts / $per_page);

// Get contact statistics
$stats_stmt = $db->prepare("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'new' THEN 1 END) as new_contacts,
        COUNT(CASE WHEN status = 'read' THEN 1 END) as read_contacts,
        COUNT(CASE WHEN status = 'replied' THEN 1 END) as replied_contacts
    FROM contacts
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contacts Management - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #740ae8 0%, #0eaaf6 100%);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(116, 10, 232, 0.05);
        }
        
        .contact-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <span style="color: #0eaaf6;">Spark</span>On
                        </h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link active" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                            <?php if ($stats['new_contacts'] > 0): ?>
                                <span class="badge bg-warning text-dark ms-auto"><?php echo $stats['new_contacts']; ?></span>
                            <?php endif; ?>
                        </a>
                        <a class="nav-link" href="categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="portfolio.php">
                            <i class="fas fa-briefcase me-2"></i>Portfolio
                        </a>
                        <a class="nav-link" href="testimonials.php">
                            <i class="fas fa-star me-2"></i>Testimonials
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Header -->
                <div class="admin-header p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-0">Contacts Management</h2>
                            <small class="text-muted">Manage customer inquiries and messages</small>
                        </div>
                        <div>
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('M j, Y'); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['total']; ?></h4>
                                <small class="text-muted">Total Contacts</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-envelope-open fa-2x text-warning mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['new_contacts']; ?></h4>
                                <small class="text-muted">New Messages</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-eye fa-2x text-info mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['read_contacts']; ?></h4>
                                <small class="text-muted">Read</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-reply fa-2x text-success mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['replied_contacts']; ?></h4>
                                <small class="text-muted">Replied</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Search Contacts</label>
                                <input type="text" class="form-control" name="search" 
                                       placeholder="Search by name, email, or message..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Filter by Status</label>
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>New</option>
                                    <option value="read" <?php echo $status_filter === 'read' ? 'selected' : ''; ?>>Read</option>
                                    <option value="replied" <?php echo $status_filter === 'replied' ? 'selected' : ''; ?>>Replied</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                                <a href="contacts.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                            <div class="col-md-2 d-flex align-items-end justify-content-end">
                                <small class="text-muted"><?php echo $total_contacts; ?> contacts found</small>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Contacts Table -->
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-envelope me-2"></i>All Contacts
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($contacts)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No contacts found</h5>
                                <p class="text-muted">
                                    <?php if ($search || $status_filter): ?>
                                        Try adjusting your search criteria or filters.
                                    <?php else: ?>
                                        No contact messages have been received yet.
                                    <?php endif; ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Contact</th>
                                            <th>Subject</th>
                                            <th>Message Preview</th>
                                            <th>Status</th>
                                            <th>Received</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($contacts as $contact): ?>
                                            <tr class="<?php echo $contact['status'] === 'new' ? 'table-warning' : ''; ?>">
                                                <td>
                                                    <div>
                                                        <div class="fw-medium"><?php echo htmlspecialchars($contact['name']); ?></div>
                                                        <small class="text-muted"><?php echo htmlspecialchars($contact['email']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="fw-medium"><?php echo htmlspecialchars($contact['subject']); ?></div>
                                                </td>
                                                <td>
                                                    <div class="contact-preview text-muted">
                                                        <?php echo htmlspecialchars(substr($contact['message'], 0, 100)) . (strlen($contact['message']) > 100 ? '...' : ''); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_classes = [
                                                        'new' => 'bg-warning',
                                                        'read' => 'bg-info',
                                                        'replied' => 'bg-success'
                                                    ];
                                                    $status_class = $status_classes[$contact['status']] ?? 'bg-secondary';
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($contact['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?php echo timeAgo($contact['created_at']); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" onclick="viewContact(<?php echo $contact['id']; ?>)" title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <?php if ($contact['status'] !== 'replied'): ?>
                                                            <button class="btn btn-outline-success" onclick="replyContact(<?php echo $contact['id']; ?>)" title="Reply">
                                                                <i class="fas fa-reply"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <button class="btn btn-outline-danger" onclick="deleteContact(<?php echo $contact['id']; ?>)" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <div class="card-footer bg-white">
                                    <nav aria-label="Contacts pagination">
                                        <ul class="pagination justify-content-center mb-0">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                                        <i class="fas fa-chevron-left"></i>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($page < $total_pages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Details Modal -->
    <div class="modal fade" id="contactModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Contact Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="contactModalBody">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="markAsRead()" id="markReadBtn">Mark as Read</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reply Modal -->
    <div class="modal fade" id="replyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reply to Contact</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reply_contact">
                        <input type="hidden" name="contact_id" id="replyContactId">
                        
                        <div class="mb-3">
                            <label class="form-label">Reply Message</label>
                            <textarea class="form-control" name="reply_message" rows="6" 
                                      placeholder="Type your reply message here..." required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Send Reply</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this contact message? This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete_contact">
                        <input type="hidden" name="contact_id" id="deleteContactId">
                        <button type="submit" class="btn btn-danger">Delete Contact</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentContactId = null;
        
        function viewContact(contactId) {
            currentContactId = contactId;
            const modal = new bootstrap.Modal(document.getElementById('contactModal'));
            const modalBody = document.getElementById('contactModalBody');
            
            // Show loading
            modalBody.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;
            
            modal.show();
            
            // Fetch contact details
            fetch(`../api/contact-details.php?id=${contactId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const contact = data.contact;
                        modalBody.innerHTML = `
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Contact Information</h6>
                                    <p><strong>Name:</strong> ${contact.name}</p>
                                    <p><strong>Email:</strong> ${contact.email}</p>
                                    <p><strong>Subject:</strong> ${contact.subject}</p>
                                    <p><strong>Status:</strong> 
                                        <span class="badge ${contact.status === 'new' ? 'bg-warning' : contact.status === 'read' ? 'bg-info' : 'bg-success'}">
                                            ${contact.status.charAt(0).toUpperCase() + contact.status.slice(1)}
                                        </span>
                                    </p>
                                    <p><strong>Received:</strong> ${new Date(contact.created_at).toLocaleString()}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold">Message</h6>
                                    <div class="border p-3 rounded bg-light">
                                        ${contact.message.replace(/\n/g, '<br>')}
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        // Update mark as read button
                        const markReadBtn = document.getElementById('markReadBtn');
                        if (contact.status === 'new') {
                            markReadBtn.style.display = 'inline-block';
                        } else {
                            markReadBtn.style.display = 'none';
                        }
                    } else {
                        modalBody.innerHTML = `
                            <div class="text-center py-4">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                                <p class="text-muted">Failed to load contact details.</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    modalBody.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                            <p class="text-muted">Error loading contact details.</p>
                        </div>
                    `;
                });
        }
        
        function markAsRead() {
            if (currentContactId) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="mark_read">
                    <input type="hidden" name="contact_id" value="${currentContactId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function replyContact(contactId) {
            document.getElementById('replyContactId').value = contactId;
            const modal = new bootstrap.Modal(document.getElementById('replyModal'));
            modal.show();
        }
        
        function deleteContact(contactId) {
            document.getElementById('deleteContactId').value = contactId;
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    </script>
</body>
</html>