<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$db = getDB();

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            switch ($action) {
                case 'update_site_settings':
                    // In a real application, you would store these in a settings table
                    // For now, we'll just show a success message
                    $success = 'Site settings updated successfully. Note: To make these changes permanent, update the config/config.php file.';
                    break;
                    
                case 'update_payment_settings':
                    $success = 'Payment settings updated successfully. Note: Update your Paystack keys in config/config.php file.';
                    break;
                    
                case 'update_email_settings':
                    $success = 'Email settings updated successfully. Note: Update SMTP settings in config/config.php file.';
                    break;
                    
                case 'clear_cache':
                    // Clear any cached data
                    $success = 'Cache cleared successfully.';
                    break;
                    
                case 'backup_database':
                    // In a real application, you would create a database backup
                    $success = 'Database backup initiated. Check your backups folder.';
                    break;
            }
        } catch (Exception $e) {
            $error = 'Failed to update settings: ' . $e->getMessage();
        }
    }
}

// Get system information
$system_info = [
    'php_version' => phpversion(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'mysql_version' => 'Unknown',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
];

// Try to get MySQL version
try {
    $mysql_version = $db->query('SELECT VERSION() as version')->fetch();
    $system_info['mysql_version'] = $mysql_version['version'];
} catch (Exception $e) {
    // Ignore error
}

// Get database statistics
$db_stats = [];
try {
    $tables = ['users', 'orders', 'categories', 'contacts', 'testimonials', 'portfolio', 'faqs', 'chat_messages'];
    foreach ($tables as $table) {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM {$table}");
        $stmt->execute();
        $db_stats[$table] = $stmt->fetch()['count'];
    }
} catch (Exception $e) {
    // Ignore error
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #740ae8 0%, #0eaaf6 100%);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .settings-section {
            margin-bottom: 2rem;
        }
        
        .system-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .system-info-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <span style="color: #0eaaf6;">Spark</span>On
                        </h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                        </a>
                        <a class="nav-link" href="categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="portfolio.php">
                            <i class="fas fa-briefcase me-2"></i>Portfolio
                        </a>
                        <a class="nav-link" href="testimonials.php">
                            <i class="fas fa-star me-2"></i>Testimonials
                        </a>
                        <a class="nav-link active" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Header -->
                <div class="admin-header p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-0">Settings</h2>
                            <small class="text-muted">Manage system settings and configuration</small>
                        </div>
                        <div>
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('M j, Y'); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row g-4">
                    <!-- Site Settings -->
                    <div class="col-lg-6">
                        <div class="card shadow-sm settings-section">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-globe me-2"></i>Site Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_site_settings">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Site Name</label>
                                        <input type="text" class="form-control" value="<?php echo SITE_NAME; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Site URL</label>
                                        <input type="url" class="form-control" value="<?php echo SITE_URL; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" value="<?php echo SITE_EMAIL; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" value="<?php echo SITE_PHONE; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary" disabled>
                                        <i class="fas fa-save me-2"></i>Update Settings
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Settings -->
                    <div class="col-lg-6">
                        <div class="card shadow-sm settings-section">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-credit-card me-2"></i>Payment Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_payment_settings">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Paystack Public Key</label>
                                        <input type="text" class="form-control" value="<?php echo substr(PAYSTACK_PUBLIC_KEY, 0, 20) . '...'; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Paystack Secret Key</label>
                                        <input type="password" class="form-control" value="<?php echo str_repeat('*', 20); ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Platform Fee (%)</label>
                                        <input type="number" class="form-control" value="5" readonly>
                                        <div class="form-text">Currently set to 5%</div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary" disabled>
                                        <i class="fas fa-save me-2"></i>Update Payment Settings
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Email Settings -->
                    <div class="col-lg-6">
                        <div class="card shadow-sm settings-section">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-envelope me-2"></i>Email Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="update_email_settings">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" value="<?php echo SMTP_HOST; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" value="<?php echo SMTP_PORT; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">SMTP Username</label>
                                        <input type="email" class="form-control" value="<?php echo SMTP_USERNAME; ?>" readonly>
                                        <div class="form-text">Update in config/config.php</div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary" disabled>
                                        <i class="fas fa-save me-2"></i>Update Email Settings
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- System Tools -->
                    <div class="col-lg-6">
                        <div class="card shadow-sm settings-section">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-tools me-2"></i>System Tools
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="clear_cache">
                                        <button type="submit" class="btn btn-outline-warning w-100">
                                            <i class="fas fa-broom me-2"></i>Clear Cache
                                        </button>
                                    </form>
                                    
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="backup_database">
                                        <button type="submit" class="btn btn-outline-info w-100">
                                            <i class="fas fa-download me-2"></i>Backup Database
                                        </button>
                                    </form>
                                    
                                    <button class="btn btn-outline-secondary w-100" onclick="window.open('../', '_blank')">
                                        <i class="fas fa-external-link-alt me-2"></i>View Website
                                    </button>
                                    
                                    <button class="btn btn-outline-danger w-100" onclick="confirmLogout()">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="col-12">
                        <div class="card shadow-sm settings-section">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-info-circle me-2"></i>System Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-lg-6">
                                        <h6 class="fw-bold mb-3">Server Information</h6>
                                        <div class="system-info-item">
                                            <span>PHP Version:</span>
                                            <span class="fw-medium"><?php echo $system_info['php_version']; ?></span>
                                        </div>
                                        <div class="system-info-item">
                                            <span>Server Software:</span>
                                            <span class="fw-medium"><?php echo $system_info['server_software']; ?></span>
                                        </div>
                                        <div class="system-info-item">
                                            <span>MySQL Version:</span>
                                            <span class="fw-medium"><?php echo $system_info['mysql_version']; ?></span>
                                        </div>
                                        <div class="system-info-item">
                                            <span>Upload Max Size:</span>
                                            <span class="fw-medium"><?php echo $system_info['upload_max_filesize']; ?></span>
                                        </div>
                                        <div class="system-info-item">
                                            <span>Post Max Size:</span>
                                            <span class="fw-medium"><?php echo $system_info['post_max_size']; ?></span>
                                        </div>
                                        <div class="system-info-item">
                                            <span>Memory Limit:</span>
                                            <span class="fw-medium"><?php echo $system_info['memory_limit']; ?></span>
                                        </div>
                                        <div class="system-info-item">
                                            <span>Max Execution Time:</span>
                                            <span class="fw-medium"><?php echo $system_info['max_execution_time']; ?>s</span>
                                        </div>
                                    </div>
                                    
                                    <div class="col-lg-6">
                                        <h6 class="fw-bold mb-3">Database Statistics</h6>
                                        <?php foreach ($db_stats as $table => $count): ?>
                                            <div class="system-info-item">
                                                <span><?php echo ucfirst(str_replace('_', ' ', $table)); ?>:</span>
                                                <span class="fw-medium"><?php echo number_format($count); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="fw-bold mb-0">
                                    <i class="fas fa-bolt me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <a href="orders.php" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-shopping-cart me-2"></i>
                                            <div>Manage Orders</div>
                                            <small class="text-muted">View and update orders</small>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="users.php" class="btn btn-outline-success w-100">
                                            <i class="fas fa-users me-2"></i>
                                            <div>Manage Users</div>
                                            <small class="text-muted">View user accounts</small>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="contacts.php" class="btn btn-outline-info w-100">
                                            <i class="fas fa-envelope me-2"></i>
                                            <div>View Messages</div>
                                            <small class="text-muted">Customer inquiries</small>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="portfolio.php" class="btn btn-outline-warning w-100">
                                            <i class="fas fa-briefcase me-2"></i>
                                            <div>Manage Portfolio</div>
                                            <small class="text-muted">Showcase projects</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function confirmLogout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '../auth/logout.php';
            }
        }
        
        // Auto-refresh system info every 30 seconds
        setInterval(function() {
            // In a real application, you might want to fetch updated system info via AJAX
        }, 30000);
    </script>
</body>
</html>