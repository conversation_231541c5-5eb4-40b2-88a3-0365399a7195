<?php
require_once '../config/config.php';

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
    
    // Also clear from database
    if (isLoggedIn()) {
        try {
            $db = getDB();
            $stmt = $db->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
        } catch (Exception $e) {
            // Ignore database errors during logout
        }
    }
}

// Destroy session
session_destroy();

// Redirect to home page
redirect('../index.php');
?>