<?php
/**
 * Production Database Configuration Template
 * SparkOn - Company Portfolio Website
 * 
 * INSTRUCTIONS:
 * 1. Copy this file to database.php
 * 2. Update the credentials with your hosting database details
 * 3. Delete this template file for security
 */

class Database {
    // UPDATE THESE WITH YOUR HOSTING DATABASE DETAILS
    private $host = 'localhost';                    // Usually 'localhost' or your hosting DB server
    private $db_name = 'your_database_name';       // Your created database name
    private $username = 'your_db_username';        // Your database username
    private $password = 'your_db_password';        // Your database password
    private $conn;

    /**
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
        } catch(PDOException $exception) {
            // Log error instead of displaying it in production
            error_log("Database connection error: " . $exception->getMessage());
            
            // Display generic error message
            die("Database connection failed. Please contact support.");
        }
        
        return $this->conn;
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return true;
            }
        } catch (Exception $e) {
            error_log("Database test failed: " . $e->getMessage());
            return false;
        }
        return false;
    }
}

/**
 * Global database connection function
 */
function getDB() {
    $database = new Database();
    return $database->getConnection();
}

/**
 * Test database connection function
 */
function testDBConnection() {
    $database = new Database();
    return $database->testConnection();
}
?>

<!-- 
HOSTING PROVIDER SPECIFIC NOTES:

1. CPANEL HOSTING:
   - Host: usually 'localhost'
   - Database name: usually 'username_databasename'
   - Username: usually 'username_dbuser'
   - Get details from cPanel > MySQL Databases

2. SHARED HOSTING:
   - Check your hosting control panel for exact details
   - Some providers use different ports or hosts
   - Contact support if connection fails

3. VPS/DEDICATED:
   - You have full control over database settings
   - May need to configure MySQL/MariaDB first
   - Ensure proper user permissions

4. CLOUD HOSTING (AWS, DigitalOcean, etc.):
   - Use the provided connection string
   - May require SSL connections
   - Check security group settings

COMMON DATABASE HOSTS BY PROVIDER:
- GoDaddy: localhost
- Bluehost: localhost  
- HostGator: localhost
- SiteGround: localhost
- Namecheap: localhost
- A2 Hosting: localhost

If 'localhost' doesn't work, try:
- 127.0.0.1
- Your server's IP address
- Provider-specific hostname

TROUBLESHOOTING:
- Ensure database exists
- Verify user has proper permissions
- Check if MySQL service is running
- Confirm PHP PDO extension is installed
- Review hosting provider documentation
-->
