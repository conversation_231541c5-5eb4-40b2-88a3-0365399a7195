<?php
require_once 'config/config.php';

// Fetch featured testimonials and portfolio items
$db = getDB();

$testimonials_stmt = $db->prepare("SELECT * FROM testimonials WHERE is_featured = 1 ORDER BY created_at DESC LIMIT 3");
$testimonials_stmt->execute();
$testimonials = $testimonials_stmt->fetchAll();

$portfolio_stmt = $db->prepare("SELECT p.*, c.name as category_name FROM portfolio p LEFT JOIN categories c ON p.category_id = c.id WHERE p.is_featured = 1 ORDER BY p.created_at DESC LIMIT 4");
$portfolio_stmt->execute();
$portfolio = $portfolio_stmt->fetchAll();

$categories_stmt = $db->prepare("SELECT * FROM categories ORDER BY name");
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - Professional Digital Services</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <img src="assets/image/logo.png" height="50px" alt="">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#portfolio">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="faq.php">FAQ</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                <li><a class="dropdown-item" href="orders.php"><i class="fas fa-list"></i> My Orders</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Sign In</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-success ms-2" href="auth/register.php">Join Now</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section bg-success text-white py-5">
        <div class="container">
            <div class="row align-items-center min-vh-75">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        Find the perfect <br>
                        <span style="color: #0eaaf6;">digital solution</span> <br>
                        for your business
                    </h1>
                    <p class="lead mb-4">
                        Work with talented professionals to bring your ideas to life. 
                        From web development to digital marketing, we've got you covered.
                    </p>
                    
                    <div class="search-box bg-white rounded-pill p-2 mb-4">
                        <div class="row g-0">
                            <div class="col">
                                <input type="text" class="form-control border-0 ps-3" placeholder="What service are you looking for today?" id="heroSearch">
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-success rounded-pill px-4" onclick="searchServices()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge bg-light text-dark">Web Development</span>
                        <span class="badge bg-light text-dark">Mobile Apps</span>
                        <span class="badge bg-light text-dark">UI/UX Design</span>
                        <span class="badge bg-light text-dark">Digital Marketing</span>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="hero-image text-center">
                        <img src="assets/image/hero-img.png" alt="Digital Services" class="img-fluid" style="max-height: 800px;">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trusted By Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <p class="text-muted mb-0 fw-medium">Trusted by:</p>
                </div>
                <div class="col-md-9">
                    <div class="d-flex justify-content-between align-items-center flex-wrap">
                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTFvpkhqGH3Dg1hcdTQ0yl4EOEdmxJXVUTFOw&s" alt="Client" class="client-logo">
                        <img src="https://upload.wikimedia.org/wikipedia/en/6/6a/University_of_Mines_and_Technology_logo.jpg" alt="Client" class="client-logo">
                        <img src="https://www.elsbeedata.site/image/icon-512.png" alt="Client" class="client-logo">
                        
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Popular Services</h2>
                <p class="text-muted">Explore our most requested digital services</p>
            </div>
            
            <div class="row g-4">
                <?php foreach ($categories as $category): ?>
                <div class="col-lg-3 col-md-6">
                    <div class="service-card h-100 p-4 rounded-3 border hover-shadow">
                        <div class="service-icon mb-3">
                            <i class="<?php echo $category['icon']; ?> fa-2x" style="color: #740ae8;"></i>
                        </div>
                        <h5 class="fw-bold mb-2"><?php echo $category['name']; ?></h5>
                        <p class="text-muted mb-3"><?php echo $category['description']; ?></p>
                        <a href="order.php?category=<?php echo $category['id']; ?>" class="btn btn-outline-success btn-sm">
                            Get Started <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">How SparkOn Works</h2>
                <p class="text-muted">Get your project done in 4 simple steps</p>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold">1</span>
                        </div>
                        <h5 class="fw-bold">Tell us what you need</h5>
                        <p class="text-muted">Describe your project requirements and budget</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold">2</span>
                        </div>
                        <h5 class="fw-bold">Make secure payment</h5>
                        <p class="text-muted">Pay securely through our integrated payment system</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold">3</span>
                        </div>
                        <h5 class="fw-bold">We work on your project</h5>
                        <p class="text-muted">Our experts bring your vision to life</p>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="text-center">
                        <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                            <span class="fw-bold">4</span>
                        </div>
                        <h5 class="fw-bold">Get your delivery</h5>
                        <p class="text-muted">Receive your completed project on time</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Featured Projects</h2>
                <p class="text-muted">Check out some of our recent work</p>
            </div>
            
            <div class="row g-4">
                <?php foreach ($portfolio as $project): ?>
                <div class="col-lg-6">
                    <div class="portfolio-card rounded-3 overflow-hidden shadow-sm">
                        <div class="portfolio-image position-relative">
                            <?php if ($project['image']): ?>
                                <img src="<?php echo htmlspecialchars($project['image']); ?>" 
                                     alt="<?php echo htmlspecialchars($project['title']); ?>" 
                                     class="img-fluid w-100" 
                                     style="height: 250px; object-fit: cover;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="d-none align-items-center justify-content-center bg-light text-muted" style="height: 250px; position: absolute; top: 0; left: 0; right: 0; bottom: 0;">
                                    <i class="fas fa-image fa-3x"></i>
                                </div>
                            <?php else: ?>
                                <div class="d-flex align-items-center justify-content-center bg-light text-muted" style="height: 250px;">
                                    <i class="fas fa-image fa-3x"></i>
                                </div>
                            <?php endif; ?>
                            <div class="portfolio-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.7); opacity: 0; transition: opacity 0.3s ease;">
                                <a href="portfolio.php" class="btn btn-light btn-sm">View Project</a>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="fw-bold mb-0"><?php echo $project['title']; ?></h5>
                                <span class="badge bg-success"><?php echo $project['category_name']; ?></span>
                            </div>
                            <p class="text-muted mb-3"><?php echo substr($project['description'], 0, 100) . '...'; ?></p>
                            <div class="d-flex flex-wrap gap-1">
                                <?php 
                                $technologies = json_decode($project['technologies'], true);
                                foreach ($technologies as $tech): 
                                ?>
                                <span class="badge bg-light text-dark"><?php echo $tech; ?></span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div class="text-center mt-5">
                <a href="portfolio.php" class="btn btn-success">View All Projects</a>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">What Our Clients Say</h2>
                <p class="text-muted">Don't just take our word for it</p>
            </div>
            
            <div class="row g-4">
                <?php foreach ($testimonials as $testimonial): ?>
                <div class="col-lg-4">
                    <div class="testimonial-card bg-white p-4 rounded-3 shadow-sm h-100">
                        <div class="d-flex mb-3">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <i class="fas fa-star <?php echo $i <= $testimonial['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                            <?php endfor; ?>
                        </div>
                        <p class="mb-4">"<?php echo $testimonial['testimonial']; ?>"</p>
                        <div class="d-flex align-items-center">
                            <div class="testimonial-avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                <?php echo strtoupper(substr($testimonial['client_name'], 0, 1)); ?>
                            </div>
                            <div>
                                <h6 class="fw-bold mb-0"><?php echo $testimonial['client_name']; ?></h6>
                                <small class="text-muted"><?php echo $testimonial['client_position']; ?>, <?php echo $testimonial['client_company']; ?></small>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="fw-bold mb-4">About SparkOn</h2>
                    <p class="lead mb-4">
                        We're a team of passionate digital professionals dedicated to helping businesses 
                        thrive in the digital world. With years of experience and a commitment to excellence, 
                        we deliver solutions that drive results.
                    </p>
                    
                    <div class="row g-4 mb-4">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="fw-bold" style="color: #740ae8;">500+</h3>
                                <p class="text-muted mb-0">Projects Completed</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="fw-bold" style="color: #740ae8;">98%</h3>
                                <p class="text-muted mb-0">Client Satisfaction</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="fw-bold" style="color: #740ae8;">50+</h3>
                                <p class="text-muted mb-0">Team Members</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="fw-bold" style="color: #740ae8;">5+</h3>
                                <p class="text-muted mb-0">Years Experience</p>
                            </div>
                        </div>
                    </div>
                    
                    <a href="order.php" class="btn btn-success">Start Your Project</a>
                </div>
                
                <div class="col-lg-6">
                    <img src="https://i.pinimg.com/736x/59/34/31/59343172d5b797fded587b777a07d441.jpg" alt="Our Team" class="img-fluid rounded-3">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Get In Touch</h2>
                <p class="text-muted">Ready to start your project? Let's talk!</p>
            </div>
            
            <div class="row g-5">
                <div class="col-lg-8">
                    <div class="bg-white p-4 rounded-3 shadow-sm">
                        <form id="contactForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Email Address</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                                <div class="col-12">
                                    <label class="form-label">Subject</label>
                                    <input type="text" class="form-control" name="subject" required>
                                </div>
                                <div class="col-12">
                                    <label class="form-label">Message</label>
                                    <textarea class="form-control" name="message" rows="5" required></textarea>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">Send Message</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="contact-info">
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-0">Phone</h6>
                                    <p class="text-muted mb-0"><?php echo SITE_PHONE; ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-0">Email</h6>
                                    <p class="text-muted mb-0"><?php echo SITE_EMAIL; ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div>
                                    <h6 class="fw-bold mb-0">Address</h6>
                                    <p class="text-muted mb-0">UMaT Campus<br>Tarkwa    , Ghana</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="social-links">
                            <h6 class="fw-bold mb-3">Follow Us</h6>
                            <div class="d-flex gap-2">
                                <a href="#" class="btn btn-outline-success btn-sm">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="btn btn-outline-success btn-sm">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="btn btn-outline-success btn-sm">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="#" class="btn btn-outline-success btn-sm">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container text-white">
            <div class="row g-4">
                <div class="col-lg-4 text-white">
                    <h5 class="fw-bold mb-3">
                        <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
                    </h5>
                    <p class="text-white mb-4">
                        Your trusted partner for digital transformation. We help businesses 
                        grow through innovative digital solutions.
                    </p>
                    <div class="d-flex gap-2">
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Services</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-decoration-none">Web Development</a></li>
                        <li><a href="#" class="text-decoration-none">Mobile Apps</a></li>
                        <li><a href="#" class="text-decoration-none">UI/UX Design</a></li>
                        <li><a href="#" class="text-decoration-none">Digital Marketing</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Company</h6>
                    <ul class="list-unstyled">
                        <li><a href="#about" class="text-decoration-none">About Us</a></li>
                        <li><a href="#portfolio" class=" text-decoration-none">Portfolio</a></li>
                        <li><a href="#contact" class=" text-decoration-none">Contact</a></li>
                        <li><a href="faq.php" class="text-decoration-none">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-decoration-none">Help Center</a></li>
                        <li><a href="#" class="text-decoration-none">Privacy Policy</a></li>
                        <li><a href="#" class="text-decoration-none">Terms of Service</a></li>
                        <li><a href="#" class="text-decoration-none">Cookie Policy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Account</h6>
                    <ul class="list-unstyled">
                        <?php if (isLoggedIn()): ?>
                            <li><a href="dashboard.php" class="text-decoration-none">Dashboard</a></li>
                            <li><a href="orders.php" class="text-decoration-none">My Orders</a></li>
                            <li><a href="auth/logout.php" class="text-decoration-none">Logout</a></li>
                        <?php else: ?>
                            <li><a href="auth/login.php" class="text-decoration-none">Sign In</a></li>
                            <li><a href="auth/register.php" class="text-decoration-none">Join Now</a></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-white mb-0">&copy; <span id="currentYear"></span> SparkOn. All rights reserved.</p>
                </div>

                <script>
                // Automatically update copyright year
                    document.getElementById('currentYear').textContent = new Date().getFullYear();
                </script>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Made with <i class="fas fa-heart text-danger"></i> By: <?php echo DEVELOPER; ?></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Chat Bot -->
    <div class="chat-widget" id="chatWidget">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </div>
        
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">SparkOn Assistant</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="bot-message">
                    <p>Hi! I'm here to help. What can I assist you with today?</p>
                </div>
            </div>
            <div class="chat-input">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                    <button class="btn btn-success" onclick="sendChatMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <style>
        .portfolio-card:hover .portfolio-overlay {
            opacity: 1 !important;
        }
        
        .portfolio-card {
            transition: transform 0.3s ease;
        }
        
        .portfolio-card:hover {
            transform: translateY(-5px);
        }
    </style>
</body>
</html>