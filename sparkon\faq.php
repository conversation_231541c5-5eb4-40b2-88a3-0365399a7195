<?php
require_once 'config/config.php';

$db = getDB();

// Get FAQs from database
$faqs_stmt = $db->prepare("SELECT * FROM faqs WHERE is_active = 1 ORDER BY order_index ASC, id ASC");
$faqs_stmt->execute();
$faqs = $faqs_stmt->fetchAll();

// Group FAQs by category
$faq_categories = [];
foreach ($faqs as $faq) {
    $category = $faq['category'] ?: 'General';
    if (!isset($faq_categories[$category])) {
        $faq_categories[$category] = [];
    }
    $faq_categories[$category][] = $faq;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frequently Asked Questions - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <img src="assets/image/logo.png" height="50px" alt="">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#services">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#portfolio">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#contact">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="faq.php">FAQ</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                                <li><a class="dropdown-item" href="orders.php"><i class="fas fa-list"></i> My Orders</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="auth/login.php">Sign In</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-success ms-2" href="auth/register.php">Join Now</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="fw-bold mb-3">Frequently Asked Questions</h1>
                    <p class="lead text-muted mb-4">
                        Find answers to common questions about our services, process, and policies
                    </p>
                    
                    <!-- Search Box -->
                    <div class="search-box bg-white rounded-pill p-2 mb-4 shadow-sm">
                        <div class="row g-0">
                            <div class="col">
                                <input type="text" class="form-control border-0 ps-3" placeholder="Search for answers..." id="faqSearch">
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-success rounded-pill px-4" onclick="searchFAQ()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- FAQ Categories Sidebar -->
                <div class="col-lg-3 mb-4">
                    <div class="card border-0 shadow-sm sticky-top" style="top: 100px;">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-list me-2"></i>Categories
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <a href="#all" class="list-group-item list-group-item-action active" onclick="filterFAQ('all')">
                                    <i class="fas fa-th-large me-2"></i>All Questions
                                </a>
                                <?php foreach (array_keys($faq_categories) as $category): ?>
                                    <a href="#<?php echo strtolower(str_replace(' ', '-', $category)); ?>" 
                                       class="list-group-item list-group-item-action" 
                                       onclick="filterFAQ('<?php echo strtolower(str_replace(' ', '-', $category)); ?>')">
                                        <i class="fas fa-folder me-2"></i><?php echo $category ?>
                                        <span class="badge bg-light text-dark float-end"><?php echo count($faq_categories[$category]); ?></span>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- FAQ Content -->
                <div class="col-lg-9">
                    <div id="faqContent">
                        <?php foreach ($faq_categories as $category => $category_faqs): ?>
                            <div class="faq-category mb-5" data-category="<?php echo strtolower(str_replace(' ', '-', $category)); ?>">
                                <h3 class="fw-bold mb-4 text-success">
                                    <i class="fas fa-question-circle me-2"></i><?php echo $category; ?>
                                </h3>
                                
                                <div class="accordion" id="accordion<?php echo str_replace(' ', '', $category); ?>">
                                    <?php foreach ($category_faqs as $index => $faq): ?>
                                        <div class="accordion-item border-0 shadow-sm mb-3 faq-item">
                                            <h2 class="accordion-header">
                                                <button class="accordion-button collapsed fw-medium" type="button" 
                                                        data-bs-toggle="collapse" 
                                                        data-bs-target="#collapse<?php echo $faq['id']; ?>">
                                                    <?php echo htmlspecialchars($faq['question']); ?>
                                                </button>
                                            </h2>
                                            <div id="collapse<?php echo $faq['id']; ?>" 
                                                 class="accordion-collapse collapse" 
                                                 data-bs-parent="#accordion<?php echo str_replace(' ', '', $category); ?>">
                                                <div class="accordion-body">
                                                    <?php echo nl2br(htmlspecialchars($faq['answer'])); ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- No Results Message -->
                    <div id="noResults" class="text-center py-5" style="display: none;">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No results found</h5>
                        <p class="text-muted">Try searching with different keywords or browse all categories.</p>
                        <button class="btn btn-success" onclick="clearSearch()">Show All Questions</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Still Have Questions Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h3 class="fw-bold mb-3">Still have questions?</h3>
                    <p class="text-muted mb-4">
                        Can't find the answer you're looking for? Our support team is here to help.
                    </p>
                    
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-comments fa-lg text-success"></i>
                                    </div>
                                    <h6 class="fw-bold">Live Chat</h6>
                                    <p class="text-muted mb-3">Chat with our AI assistant for instant answers</p>
                                    <button class="btn btn-outline-success btn-sm" onclick="toggleChat()">Start Chat</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-envelope fa-lg text-info"></i>
                                    </div>
                                    <h6 class="fw-bold">Email Support</h6>
                                    <p class="text-muted mb-3">Send us an email and we'll respond within 24 hours</p>
                                    <a href="mailto:<?php echo SITE_EMAIL; ?>" class="btn btn-outline-info btn-sm">Send Email</a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-phone fa-lg text-warning"></i>
                                    </div>
                                    <h6 class="fw-bold">Phone Support</h6>
                                    <p class="text-muted mb-3">Call us during business hours for immediate help</p>
                                    <a href="tel:<?php echo SITE_PHONE; ?>" class="btn btn-outline-warning btn-sm">Call Now</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3">
                        <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
                    </h5>
                    <p class="text-muted mb-4">
                        Your trusted partner for digital transformation. We help businesses 
                        grow through innovative digital solutions.
                    </p>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-muted text-decoration-none">Home</a></li>
                        <li><a href="index.php#services" class="text-muted text-decoration-none">Services</a></li>
                        <li><a href="index.php#portfolio" class="text-muted text-decoration-none">Portfolio</a></li>
                        <li><a href="index.php#about" class="text-muted text-decoration-none">About</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="faq.php" class="text-muted text-decoration-none">FAQ</a></li>
                        <li><a href="index.php#contact" class="text-muted text-decoration-none">Contact</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Terms</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Privacy</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">Contact Info</h6>
                    <p class="text-muted mb-1">
                        <i class="fas fa-envelope me-2"></i><?php echo SITE_EMAIL; ?>
                    </p>
                    <p class="text-muted mb-1">
                        <i class="fas fa-phone me-2"></i><?php echo SITE_PHONE; ?>
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt me-2"></i>123 Business Street, Accra, Ghana
                    </p>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2024 SparkOn. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Made with <i class="fas fa-heart text-danger"></i> in Ghana</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Chat Bot -->
    <div class="chat-widget" id="chatWidget">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </div>
        
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">SparkOn Assistant</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="bot-message">
                    <p>Hi! I can help you find answers to your questions. What would you like to know?</p>
                </div>
            </div>
            <div class="chat-input">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                    <button class="btn btn-success" onclick="sendChatMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // FAQ Search functionality
        function searchFAQ() {
            const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
            const faqItems = document.querySelectorAll('.faq-item');
            const noResults = document.getElementById('noResults');
            let hasResults = false;
            
            faqItems.forEach(item => {
                const question = item.querySelector('.accordion-button').textContent.toLowerCase();
                const answer = item.querySelector('.accordion-body').textContent.toLowerCase();
                
                if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                    item.style.display = 'block';
                    hasResults = true;
                } else {
                    item.style.display = 'none';
                }
            });
            
            // Show/hide categories based on visible items
            document.querySelectorAll('.faq-category').forEach(category => {
                const visibleItems = category.querySelectorAll('.faq-item[style="display: block;"], .faq-item:not([style])');
                if (visibleItems.length === 0) {
                    category.style.display = 'none';
                } else {
                    category.style.display = 'block';
                }
            });
            
            noResults.style.display = hasResults ? 'none' : 'block';
        }
        
        // Filter FAQ by category
        function filterFAQ(category) {
            const categories = document.querySelectorAll('.faq-category');
            const sidebarLinks = document.querySelectorAll('.list-group-item');
            
            // Update active sidebar link
            sidebarLinks.forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
            
            // Show/hide categories
            categories.forEach(cat => {
                if (category === 'all' || cat.dataset.category === category) {
                    cat.style.display = 'block';
                } else {
                    cat.style.display = 'none';
                }
            });
            
            document.getElementById('noResults').style.display = 'none';
        }
        
        // Clear search
        function clearSearch() {
            document.getElementById('faqSearch').value = '';
            document.querySelectorAll('.faq-item').forEach(item => {
                item.style.display = 'block';
            });
            document.querySelectorAll('.faq-category').forEach(category => {
                category.style.display = 'block';
            });
            document.getElementById('noResults').style.display = 'none';
        }
        
        // Search on Enter key
        document.getElementById('faqSearch').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchFAQ();
            }
        });
        
        // Real-time search
        document.getElementById('faqSearch').addEventListener('input', function() {
            if (this.value.length > 2 || this.value.length === 0) {
                searchFAQ();
            }
        });
    </script>
</body>
</html>