<?php
require_once 'config/config.php';

// Check if user is admin (you might want to add this check)
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

// Get PHP error log
$error_log_file = ini_get('error_log');
if (!$error_log_file) {
    // Try common locations
    $possible_logs = [
        'C:\xampp\php\logs\php_error_log',
        'C:\xampp\apache\logs\error.log',
        'C:\Windows\temp\php-errors.log',
        dirname(__FILE__) . '/error.log'
    ];
    
    foreach ($possible_logs as $log_file) {
        if (file_exists($log_file)) {
            $error_log_file = $log_file;
            break;
        }
    }
}

$logs = [];
if ($error_log_file && file_exists($error_log_file)) {
    $log_content = file_get_contents($error_log_file);
    $log_lines = explode("\n", $log_content);
    
    // Get last 50 lines and filter for payment-related logs
    $recent_lines = array_slice($log_lines, -100);
    foreach ($recent_lines as $line) {
        if (stripos($line, 'payment') !== false || 
            stripos($line, 'paystack') !== false || 
            stripos($line, 'verification') !== false) {
            $logs[] = $line;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Logs - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-4">Payment Verification Logs</h1>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Payment-Related Log Entries</h5>
                        <small class="text-muted">Log file: <?php echo $error_log_file ?: 'Not found'; ?></small>
                    </div>
                    <div class="card-body">
                        <?php if (empty($logs)): ?>
                            <p class="text-muted">No payment-related log entries found.</p>
                            <p><strong>Log file location:</strong> <?php echo $error_log_file ?: 'Could not determine log file location'; ?></p>
                        <?php else: ?>
                            <div class="log-entries" style="max-height: 500px; overflow-y: auto;">
                                <?php foreach (array_reverse($logs) as $log): ?>
                                    <div class="log-entry mb-2 p-2 bg-light rounded">
                                        <small class="font-monospace"><?php echo htmlspecialchars($log); ?></small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-refresh me-2"></i>Refresh Logs
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>