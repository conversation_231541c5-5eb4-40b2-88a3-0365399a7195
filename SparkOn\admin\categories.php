<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$db = getDB();

// Handle category actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            switch ($action) {
                case 'add_category':
                    $name = sanitize($_POST['name']);
                    $description = sanitize($_POST['description']);
                    $icon = sanitize($_POST['icon']);
                    
                    if (empty($name)) {
                        $error = 'Category name is required.';
                    } else {
                        $stmt = $db->prepare("INSERT INTO categories (name, description, icon) VALUES (?, ?, ?)");
                        $stmt->execute([$name, $description, $icon]);
                        $success = 'Category added successfully.';
                    }
                    break;
                    
                case 'edit_category':
                    $category_id = (int)$_POST['category_id'];
                    $name = sanitize($_POST['name']);
                    $description = sanitize($_POST['description']);
                    $icon = sanitize($_POST['icon']);
                    
                    if (empty($name)) {
                        $error = 'Category name is required.';
                    } else {
                        $stmt = $db->prepare("UPDATE categories SET name = ?, description = ?, icon = ? WHERE id = ?");
                        $stmt->execute([$name, $description, $icon, $category_id]);
                        $success = 'Category updated successfully.';
                    }
                    break;
                    
                case 'delete_category':
                    $category_id = (int)$_POST['category_id'];
                    
                    // Check if category has orders
                    $check_stmt = $db->prepare("SELECT COUNT(*) as count FROM orders WHERE category_id = ?");
                    $check_stmt->execute([$category_id]);
                    $order_count = $check_stmt->fetch()['count'];
                    
                    if ($order_count > 0) {
                        $error = "Cannot delete category. It has {$order_count} associated orders.";
                    } else {
                        $stmt = $db->prepare("DELETE FROM categories WHERE id = ?");
                        $stmt->execute([$category_id]);
                        $success = 'Category deleted successfully.';
                    }
                    break;
            }
        } catch (Exception $e) {
            $error = 'Failed to perform action: ' . $e->getMessage();
        }
    }
}

// Get categories with order counts
$categories_stmt = $db->prepare("
    SELECT c.*, 
           COUNT(o.id) as order_count,
           COUNT(p.id) as portfolio_count
    FROM categories c 
    LEFT JOIN orders o ON c.id = o.category_id 
    LEFT JOIN portfolio p ON c.id = p.category_id 
    GROUP BY c.id
    ORDER BY c.name
");
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll();

// Get category statistics
$stats_stmt = $db->prepare("
    SELECT 
        COUNT(*) as total_categories,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_categories
    FROM categories
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories Management - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #740ae8 0%, #0eaaf6 100%);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .category-card {
            transition: transform 0.3s ease;
        }
        
        .category-card:hover {
            transform: translateY(-5px);
        }
        
        .category-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <span style="color: #0eaaf6;">Spark</span>On
                        </h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                        </a>
                        <a class="nav-link active" href="categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="portfolio.php">
                            <i class="fas fa-briefcase me-2"></i>Portfolio
                        </a>
                        <a class="nav-link" href="testimonials.php">
                            <i class="fas fa-star me-2"></i>Testimonials
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Header -->
                <div class="admin-header p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-0">Categories Management</h2>
                            <small class="text-muted">Manage service categories and classifications</small>
                        </div>
                        <div>
                            <button class="btn btn-success" onclick="addCategory()">
                                <i class="fas fa-plus me-2"></i>Add Category
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-6 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-tags fa-2x text-primary mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['total_categories']; ?></h4>
                                <small class="text-muted">Total Categories</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-6 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-plus-circle fa-2x text-success mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['new_categories']; ?></h4>
                                <small class="text-muted">New (30 days)</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Categories Grid -->
                <div class="row g-4">
                    <?php foreach ($categories as $category): ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="card category-card shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="category-icon bg-primary bg-opacity-10 text-primary">
                                        <i class="<?php echo $category['icon'] ?: 'fas fa-folder'; ?>"></i>
                                    </div>
                                    
                                    <h5 class="fw-bold mb-2"><?php echo htmlspecialchars($category['name']); ?></h5>
                                    <p class="text-muted mb-3"><?php echo htmlspecialchars($category['description'] ?: 'No description'); ?></p>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <h6 class="fw-bold text-primary mb-0"><?php echo $category['order_count']; ?></h6>
                                            <small class="text-muted">Orders</small>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="fw-bold text-success mb-0"><?php echo $category['portfolio_count']; ?></h6>
                                            <small class="text-muted">Portfolio</small>
                                        </div>
                                    </div>
                                    
                                    <div class="btn-group w-100">
                                        <button class="btn btn-outline-primary btn-sm" onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>', <?php echo $category['order_count']; ?>)">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <?php if (empty($categories)): ?>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No categories found</h5>
                                <p class="text-muted mb-3">Start by adding your first service category.</p>
                                <button class="btn btn-success" onclick="addCategory()">
                                    <i class="fas fa-plus me-2"></i>Add First Category
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Category Modal -->
    <div class="modal fade" id="categoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="categoryModalTitle">Add Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="categoryForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="categoryAction" value="add_category">
                        <input type="hidden" name="category_id" id="categoryId">
                        
                        <div class="mb-3">
                            <label class="form-label">Category Name *</label>
                            <input type="text" class="form-control" name="name" id="categoryName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" id="categoryDescription" rows="3" 
                                      placeholder="Brief description of this category..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Icon Class</label>
                            <input type="text" class="form-control" name="icon" id="categoryIcon" 
                                   placeholder="e.g., fas fa-code, fas fa-paint-brush">
                            <div class="form-text">
                                Use Font Awesome icon classes. 
                                <a href="https://fontawesome.com/icons" target="_blank">Browse icons</a>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Icon Preview</label>
                            <div class="border rounded p-3 text-center">
                                <i id="iconPreview" class="fas fa-folder fa-2x text-primary"></i>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success" id="categorySubmitBtn">Add Category</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the category "<span id="deleteCategoryName"></span>"?</p>
                    <div id="deleteWarning" class="alert alert-warning" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This category has associated orders and cannot be deleted.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" style="display: inline;" id="deleteForm">
                        <input type="hidden" name="action" value="delete_category">
                        <input type="hidden" name="category_id" id="deleteCategoryId">
                        <button type="submit" class="btn btn-danger" id="deleteSubmitBtn">Delete Category</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addCategory() {
            document.getElementById('categoryModalTitle').textContent = 'Add Category';
            document.getElementById('categoryAction').value = 'add_category';
            document.getElementById('categorySubmitBtn').textContent = 'Add Category';
            document.getElementById('categoryForm').reset();
            document.getElementById('iconPreview').className = 'fas fa-folder fa-2x text-primary';
            
            const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
            modal.show();
        }
        
        function editCategory(category) {
            document.getElementById('categoryModalTitle').textContent = 'Edit Category';
            document.getElementById('categoryAction').value = 'edit_category';
            document.getElementById('categorySubmitBtn').textContent = 'Update Category';
            document.getElementById('categoryId').value = category.id;
            document.getElementById('categoryName').value = category.name;
            document.getElementById('categoryDescription').value = category.description || '';
            document.getElementById('categoryIcon').value = category.icon || '';
            
            // Update icon preview
            const iconClass = category.icon || 'fas fa-folder';
            document.getElementById('iconPreview').className = iconClass + ' fa-2x text-primary';
            
            const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
            modal.show();
        }
        
        function deleteCategory(categoryId, categoryName, orderCount) {
            document.getElementById('deleteCategoryId').value = categoryId;
            document.getElementById('deleteCategoryName').textContent = categoryName;
            
            const deleteWarning = document.getElementById('deleteWarning');
            const deleteSubmitBtn = document.getElementById('deleteSubmitBtn');
            
            if (orderCount > 0) {
                deleteWarning.style.display = 'block';
                deleteSubmitBtn.disabled = true;
                deleteSubmitBtn.textContent = 'Cannot Delete';
            } else {
                deleteWarning.style.display = 'none';
                deleteSubmitBtn.disabled = false;
                deleteSubmitBtn.textContent = 'Delete Category';
            }
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
        
        // Icon preview functionality
        document.getElementById('categoryIcon').addEventListener('input', function() {
            const iconClass = this.value.trim() || 'fas fa-folder';
            const iconPreview = document.getElementById('iconPreview');
            iconPreview.className = iconClass + ' fa-2x text-primary';
        });
        
        // Form validation
        document.getElementById('categoryForm').addEventListener('submit', function(e) {
            const name = document.getElementById('categoryName').value.trim();
            if (!name) {
                e.preventDefault();
                alert('Category name is required.');
                return;
            }
        });
    </script>
</body>
</html>