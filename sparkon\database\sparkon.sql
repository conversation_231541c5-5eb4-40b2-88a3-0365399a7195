-- SparkOn Database Schema
-- Company Portfolio Website with Project Ordering System

CREATE DATABASE IF NOT EXISTS sparkon;
USE sparkon;

-- Users table for authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHA<PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('client', 'admin') DEFAULT 'client',
    profile_image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Project categories
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Project orders/requests
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    category_id INT,
    project_title VARCHAR(255) NOT NULL,
    project_description TEXT NOT NULL,
    budget DECIMAL(10,2) NOT NULL,
    deadline DATE NOT NULL,
    status ENUM('pending', 'paid', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed') DEFAULT 'pending',
    payment_reference VARCHAR(100),
    files JSON,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Contact form submissions
CREATE TABLE contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(200),
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Testimonials
CREATE TABLE testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_name VARCHAR(100) NOT NULL,
    client_position VARCHAR(100),
    client_company VARCHAR(100),
    testimonial TEXT NOT NULL,
    rating INT DEFAULT 5,
    client_image VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Portfolio/Projects showcase
CREATE TABLE portfolio (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category_id INT,
    image VARCHAR(255),
    project_url VARCHAR(255),
    technologies JSON,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Chat messages for chatbot
CREATE TABLE chat_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    user_message TEXT,
    bot_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- FAQ section
CREATE TABLE faqs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question VARCHAR(500) NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    order_index INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default categories
INSERT INTO categories (name, description, icon) VALUES
('Web Development', 'Custom websites and web applications', 'fas fa-code'),
('Mobile Apps', 'iOS and Android mobile applications', 'fas fa-mobile-alt'),
('UI/UX Design', 'User interface and experience design', 'fas fa-paint-brush'),
('Digital Marketing', 'SEO, social media, and online marketing', 'fas fa-bullhorn'),
('Graphic Design', 'Logos, branding, and visual design', 'fas fa-palette'),
('Content Writing', 'Blog posts, copywriting, and content creation', 'fas fa-pen'),
('Data Analysis', 'Business intelligence and data insights', 'fas fa-chart-bar'),
('Consulting', 'Business and technical consulting services', 'fas fa-handshake');

-- Insert sample testimonials
INSERT INTO testimonials (client_name, client_position, client_company, testimonial, rating, is_featured) VALUES
('Sarah Johnson', 'Marketing Director', 'TechCorp Inc.', 'SparkOn delivered an exceptional website that exceeded our expectations. Their attention to detail and professional approach made the entire process seamless.', 5, TRUE),
('Michael Chen', 'CEO', 'StartupHub', 'The mobile app they developed for us has been a game-changer. User engagement increased by 300% after launch. Highly recommended!', 5, TRUE),
('Emily Rodriguez', 'Brand Manager', 'Creative Studios', 'Outstanding design work! They perfectly captured our brand vision and delivered a stunning visual identity that resonates with our target audience.', 5, TRUE),
('David Thompson', 'Operations Manager', 'LogiFlow', 'Professional, reliable, and results-driven. SparkOn helped streamline our business processes with their custom software solution.', 5, FALSE);

-- Insert sample portfolio items
INSERT INTO portfolio (title, description, category_id, technologies, is_featured) VALUES
('E-commerce Platform', 'Full-featured online store with payment integration and inventory management', 1, '["PHP", "MySQL", "Bootstrap", "JavaScript"]', TRUE),
('Fitness Tracking App', 'Mobile application for tracking workouts and nutrition with social features', 2, '["React Native", "Node.js", "MongoDB"]', TRUE),
('Brand Identity Package', 'Complete branding solution including logo, color palette, and style guide', 5, '["Adobe Illustrator", "Photoshop", "Figma"]', TRUE),
('Business Analytics Dashboard', 'Real-time dashboard for tracking KPIs and business metrics', 7, '["Python", "Django", "Chart.js", "PostgreSQL"]', FALSE);

-- Insert FAQ items
INSERT INTO faqs (question, answer, category, order_index) VALUES
('What services does SparkOn offer?', 'We offer a comprehensive range of digital services including web development, mobile app development, UI/UX design, digital marketing, graphic design, content writing, data analysis, and business consulting.', 'Services', 1),
('How long does a typical project take?', 'Project timelines vary depending on complexity and scope. Simple websites typically take 1-2 weeks, while complex applications can take 2-3 months. We provide detailed timelines during the consultation phase.', 'Timeline', 2),
('What is your payment process?', 'We require a 50% deposit to begin work, with the remaining balance due upon project completion. We accept payments through Paystack and offer flexible payment plans for larger projects.', 'Payment', 3),
('Do you provide ongoing support?', 'Yes! We offer comprehensive post-launch support including maintenance, updates, and technical assistance. Support packages are available based on your specific needs.', 'Support', 4),
('Can you work with my existing brand guidelines?', 'Absolutely! We work closely with your existing brand guidelines and can also help develop new branding elements if needed. Our design team ensures consistency across all deliverables.', 'Branding', 5),
('How do I track my project progress?', 'Once you place an order, you\'ll receive access to our client portal where you can track project milestones, communicate with your team, and view deliverables in real-time.', 'Process', 6);

-- Create admin user (username: sparkon_admin, password: selables_GH)
INSERT INTO users (full_name, email, password, role) VALUES
('SparkOn Administrator', '<EMAIL>', '$2y$10$RuC.DgDBCq1p2qy5T96erOZpOcEXKcm6Q6y0Q5C0nbRW4NJmaSaVO', 'admin');