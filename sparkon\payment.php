<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

// Get order ID from URL
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

if (!$order_id) {
    redirect('dashboard.php');
}

$db = getDB();

// Get order details
$stmt = $db->prepare("
    SELECT o.*, c.name as category_name, u.full_name, u.email 
    FROM orders o 
    LEFT JOIN categories c ON o.category_id = c.id 
    LEFT JOIN users u ON o.user_id = u.id 
    WHERE o.id = ? AND o.user_id = ?
");
$stmt->execute([$order_id, $_SESSION['user_id']]);
$order = $stmt->fetch();

if (!$order) {
    redirect('dashboard.php');
}

// Check if already paid
if ($order['payment_status'] === 'paid') {
    redirect("order-tracking.php?order_id={$order_id}");
}

// Calculate fees (platform takes 5% + payment processing fee)
$platform_fee = $order['budget'] * 0.05; // 5% platform fee
$paystack_fee = ($order['budget'] + $platform_fee) * 0.015; // 1.5% Paystack fee
$total_amount = $order['budget'] + $platform_fee + $paystack_fee;

// Generate payment reference
$payment_reference = 'SPARK_' . $order_id . '_' . time();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Paystack Inline JS -->
    <script src="https://js.paystack.co/v1/inline.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                </span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="fw-bold">Complete Your Payment</h1>
                    <p class="text-muted">Secure payment powered by Paystack</p>
                </div>

                <!-- Progress Steps -->
                <div class="row mb-5">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                <i class="fas fa-check"></i>
                            </div>
                            <h6 class="fw-bold text-success">Project Details</h6>
                            <small class="text-success">Completed</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                <span class="fw-bold">2</span>
                            </div>
                            <h6 class="fw-bold text-success">Payment</h6>
                            <small class="text-success">Current Step</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="step-icon bg-light text-muted rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                <span class="fw-bold">3</span>
                            </div>
                            <h6 class="fw-bold text-muted">Project Starts</h6>
                            <small class="text-muted">Next Step</small>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    <!-- Order Summary -->
                    <div class="col-lg-7">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-alt me-2"></i>Order Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6 class="fw-bold"><?php echo htmlspecialchars($order['project_title']); ?></h6>
                                    <?php if ($order['category_name']): ?>
                                        <span class="badge bg-success"><?php echo $order['category_name']; ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>Description:</strong>
                                    <p class="text-muted mb-0"><?php echo nl2br(htmlspecialchars(substr($order['project_description'], 0, 200))); ?>...</p>
                                </div>
                                
                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <strong>Budget:</strong>
                                        <div class="text-success fw-bold"><?php echo formatCurrency($order['budget']); ?></div>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Deadline:</strong>
                                        <div><?php echo date('M j, Y', strtotime($order['deadline'])); ?></div>
                                    </div>
                                </div>
                                
                                <?php if ($order['files']): ?>
                                    <div class="mt-3">
                                        <strong>Attached Files:</strong>
                                        <div class="mt-1">
                                            <?php 
                                            $files = json_decode($order['files'], true);
                                            foreach ($files as $file): 
                                            ?>
                                                <span class="badge bg-light text-dark me-1">
                                                    <i class="fas fa-file me-1"></i><?php echo $file['original_name']; ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Security Notice -->
                        <div class="card mt-4 border-success">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-shield-alt fa-2x text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold mb-1">Secure Payment</h6>
                                        <p class="text-muted mb-0">
                                            Your payment is secured by Paystack's industry-leading encryption. 
                                            We never store your card details.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    <div class="col-lg-5">
                        <div class="card shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-credit-card me-2"></i>Payment Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="payment-breakdown">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Project Budget:</span>
                                        <span><?php echo formatCurrency($order['budget']); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Platform Fee (5%):</span>
                                        <span><?php echo formatCurrency($platform_fee); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-3">
                                        <span>Payment Processing:</span>
                                        <span><?php echo formatCurrency($paystack_fee); ?></span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between mb-4">
                                        <strong>Total Amount:</strong>
                                        <strong class="text-success"><?php echo formatCurrency($total_amount); ?></strong>
                                    </div>
                                </div>
                                
                                <button id="payNowButton" class="btn btn-success btn-lg w-100 mb-3" onclick="payWithPaystack()">
                                    <i class="fas fa-lock me-2"></i>Pay Now
                                </button>
                                
                                <div class="text-center">
                                    <small class="text-muted">
                                        By proceeding, you agree to our 
                                        <a href="#" class="text-success">Terms of Service</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Methods -->
                        <div class="card mt-4">
                            <div class="card-body text-center">
                                <h6 class="fw-bold mb-3">We Accept</h6>
                                <div class="d-flex justify-content-center align-items-center gap-3">
                                    <img src="assets/images/visa.png" alt="Visa" style="height: 30px;">
                                    <img src="assets/images/mastercard.png" alt="Mastercard" style="height: 30px;">
                                    <img src="assets/images/momo.png" alt="Mobile Money" style="height: 30px;">
                                    <img src="assets/images/bank.png" alt="Bank Transfer" style="height: 30px;">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Money Back Guarantee -->
                        <div class="card mt-4 bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-medal fa-2x text-warning mb-2"></i>
                                <h6 class="fw-bold">100% Money Back Guarantee</h6>
                                <p class="text-muted mb-0 small">
                                    If you're not satisfied with the final result, we'll refund your money.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Back Button -->
                <div class="text-center mt-4">
                    <a href="order.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Project Details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function payWithPaystack() {
            console.log('payWithPaystack function called');
            
            // Check if PaystackPop is available
            if (typeof PaystackPop === 'undefined') {
                console.error('PaystackPop is not defined. Paystack script may not be loaded.');
                alert('Payment system is not available. Please refresh the page and try again.');
                return;
            }
            
            console.log('PaystackPop is available');
            
            try {
                let handler = PaystackPop.setup({
                    key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
                    email: '<?php echo htmlspecialchars($order['email']); ?>',
                    amount: <?php echo (int)($total_amount * 100); ?>, // Amount in pesewas
                    currency: 'GHS',
                    ref: '<?php echo $payment_reference; ?>',
                    metadata: {
                        order_id: <?php echo $order_id; ?>,
                        user_id: <?php echo $_SESSION['user_id']; ?>,
                        project_title: '<?php echo addslashes(htmlspecialchars($order['project_title'])); ?>'
                    },
                    callback: function(response) {
                        console.log('Payment successful, reference:', response.reference);
                        // Payment successful
                        verifyPayment(response.reference);
                    },
                    onClose: function() {
                        // Payment cancelled
                        console.log('Payment cancelled');
                    }
                });
                
                console.log('Paystack handler created, opening iframe...');
                handler.openIframe();
                
            } catch (error) {
                console.error('Error setting up Paystack:', error);
                alert('Error setting up payment: ' + error.message);
            }
        }
        
        function verifyPayment(reference) {
            console.log('Starting payment verification for reference:', reference);
            
            // Show loading state
            const payButton = document.getElementById('payNowButton');
            const originalText = payButton.innerHTML;
            const originalClass = payButton.className;
            
            payButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Verifying Payment...';
            payButton.disabled = true;
            payButton.className = 'btn btn-warning btn-lg w-100 mb-3';
            
            // Prepare verification data
            const verificationData = {
                reference: reference,
                order_id: <?php echo $order['id']; ?>
            };
            
            console.log('Sending verification data:', verificationData);
            
            // Send verification request to server
            fetch('api/verify-payment.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(verificationData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }
                
                return response.text(); // Get as text first to debug
            })
            .then(responseText => {
                console.log('Raw response:', responseText);
                
                // Try to parse JSON
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    throw new Error('Invalid JSON response from server');
                }
                
                console.log('Parsed verification response:', data);
                
                if (data.success) {
                    // Payment verified successfully
                    console.log('Payment verification successful!');
                    
                    payButton.innerHTML = '<i class="fas fa-check me-2"></i>Payment Verified!';
                    payButton.className = 'btn btn-success btn-lg w-100 mb-3';
                    
                    // Show success message
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success mt-3';
                    successAlert.innerHTML = '<i class="fas fa-check-circle me-2"></i>Payment verified successfully! Redirecting...';
                    payButton.parentNode.appendChild(successAlert);
                    
                    // Redirect after showing success message
                    setTimeout(() => {
                        window.location.href = 'order-tracking.php?order_id=<?php echo $order_id; ?>&payment=success';
                    }, 2000);
                    
                } else {
                    // Payment verification failed
                    const errorMessage = data.message || 'Payment verification failed. Please contact support.';
                    console.error('Verification failed:', errorMessage);
                    
                    // Reset button
                    payButton.innerHTML = originalText;
                    payButton.className = originalClass;
                    payButton.disabled = false;
                    
                    // Show error message
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger mt-3';
                    errorAlert.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i><strong>Verification Failed:</strong> ${errorMessage}`;
                    payButton.parentNode.appendChild(errorAlert);
                    
                    // Offer manual verification after a delay
                    setTimeout(() => {
                        if (confirm(`Payment Verification Failed\n\nError: ${errorMessage}\n\nYour payment may have been processed but verification failed.\n\nWould you like to try manual verification?\n\nClick OK to go to manual verification page.`)) {
                            window.location.href = `manual-verify.php?order_id=<?php echo $order_id; ?>`;
                        }
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Verification error:', error);
                
                // Reset button
                payButton.innerHTML = originalText;
                payButton.className = originalClass;
                payButton.disabled = false;
                
                // Show error message
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger mt-3';
                errorAlert.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i><strong>Network Error:</strong> ${error.message}`;
                payButton.parentNode.appendChild(errorAlert);
                
                // Offer manual verification
                setTimeout(() => {
                    if (confirm(`Network Error\n\nFailed to verify payment due to a network error.\n\nPayment Reference: ${reference}\n\nYour payment may have been processed. Would you like to try manual verification?\n\nClick OK to go to manual verification page.`)) {
                        window.location.href = `manual-verify.php?order_id=<?php echo $order_id; ?>`;
                    }
                }, 2000);
            });
        }
        
        // Debug: Check if page loads correctly
        window.addEventListener('load', function() {
            console.log('Payment page loaded successfully');
            console.log('PaystackPop available:', typeof PaystackPop !== 'undefined');
            console.log('Payment button found:', document.getElementById('payNowButton') !== null);
            
            // Show alert if Paystack is not available
            if (typeof PaystackPop === 'undefined') {
                console.error('PaystackPop not loaded!');
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning mt-3';
                alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Payment system is loading... Please wait and try again.';
                const button = document.getElementById('payNowButton');
                if (button) {
                    button.parentNode.appendChild(alertDiv);
                }
            }
            
            // Add click event listener for debugging
            const payButton = document.getElementById('payNowButton');
            if (payButton) {
                payButton.addEventListener('click', function(e) {
                    console.log('Pay button clicked!');
                });
            }
        });
        
        // Add error handling for script loading
        window.addEventListener('error', function(e) {
            console.error('Script error:', e.error);
            if (e.filename && e.filename.includes('paystack')) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger mt-3';
                alertDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Failed to load payment system. Please refresh the page.';
                const button = document.getElementById('payNowButton');
                if (button) {
                    button.parentNode.appendChild(alertDiv);
                }
            }
        });
        
        // Prevent back button after payment starts
        window.addEventListener('beforeunload', function(e) {
            // Only show warning if payment is in progress
            const payButton = document.getElementById('payNowButton');
            if (payButton && payButton.disabled) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
</body>
</html>