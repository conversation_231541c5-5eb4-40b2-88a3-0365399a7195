<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

$db = getDB();

// Get categories for dropdown
$categories_stmt = $db->prepare("SELECT * FROM categories ORDER BY name");
$categories_stmt->execute();
$categories = $categories_stmt->fetchAll();

// Get selected category if provided
$selected_category = isset($_GET['category']) ? (int)$_GET['category'] : 0;

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $project_title = sanitize($_POST['project_title']);
    $project_description = sanitize($_POST['project_description']);
    $category_id = (int)$_POST['category_id'];
    $budget = (float)$_POST['budget'];
    $deadline = $_POST['deadline'];
    
    // Validation
    if (empty($project_title) || empty($project_description) || empty($budget) || empty($deadline)) {
        $error = 'Please fill in all required fields.';
    } elseif ($budget < 50) {
        $error = 'Minimum budget is ₵50.';
    } elseif (strtotime($deadline) <= time()) {
        $error = 'Deadline must be in the future.';
    } else {
        try {
            // Handle file uploads
            $uploaded_files = [];
            if (isset($_FILES['project_files']) && $_FILES['project_files']['error'][0] !== UPLOAD_ERR_NO_FILE) {
                $upload_dir = 'uploads/projects/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                foreach ($_FILES['project_files']['tmp_name'] as $key => $tmp_name) {
                    if ($_FILES['project_files']['error'][$key] === UPLOAD_ERR_OK) {
                        $file_name = $_FILES['project_files']['name'][$key];
                        $file_size = $_FILES['project_files']['size'][$key];
                        
                        // Validate file
                        if ($file_size > MAX_FILE_SIZE) {
                            $error = 'File size must be less than 5MB.';
                            break;
                        }
                        
                        $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'zip'];
                        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                        
                        if (!in_array($file_ext, $allowed_types)) {
                            $error = 'Invalid file type. Allowed: ' . implode(', ', $allowed_types);
                            break;
                        }
                        
                        $new_filename = generateUniqueFilename($file_name);
                        $upload_path = $upload_dir . $new_filename;
                        
                        if (move_uploaded_file($tmp_name, $upload_path)) {
                            $uploaded_files[] = [
                                'original_name' => $file_name,
                                'stored_name' => $new_filename,
                                'path' => $upload_path,
                                'size' => $file_size
                            ];
                        }
                    }
                }
            }
            
            if (!$error) {
                // Insert order into database
                $stmt = $db->prepare("
                    INSERT INTO orders (user_id, category_id, project_title, project_description, budget, deadline, files, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
                ");
                
                $files_json = !empty($uploaded_files) ? json_encode($uploaded_files) : null;
                $stmt->execute([
                    $_SESSION['user_id'],
                    $category_id ?: null,
                    $project_title,
                    $project_description,
                    $budget,
                    $deadline,
                    $files_json
                ]);
                
                $order_id = $db->lastInsertId();
                
                // Redirect to payment page
                redirect("payment.php?order_id={$order_id}");
            }
        } catch (Exception $e) {
            $error = 'Failed to submit order. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Project Request - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">My Orders</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                            <li><a class="dropdown-item" href="orders.php"><i class="fas fa-list"></i> My Orders</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="fw-bold">Submit Your Project Request</h1>
                    <p class="text-muted">Tell us about your project and we'll bring it to life</p>
                </div>

                <!-- Progress Steps -->
                <div class="row mb-5">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="step-icon bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                <span class="fw-bold">1</span>
                            </div>
                            <h6 class="fw-bold text-success">Project Details</h6>
                            <small class="text-muted">Current Step</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="step-icon bg-light text-muted rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                <span class="fw-bold">2</span>
                            </div>
                            <h6 class="fw-bold text-muted">Payment</h6>
                            <small class="text-muted">Next Step</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="step-icon bg-light text-muted rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 40px; height: 40px;">
                                <span class="fw-bold">3</span>
                            </div>
                            <h6 class="fw-bold text-muted">Project Starts</h6>
                            <small class="text-muted">Final Step</small>
                        </div>
                    </div>
                </div>

                <!-- Order Form -->
                <div class="card shadow-sm">
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" enctype="multipart/form-data" id="orderForm">
                            <div class="row g-4">
                                <!-- Project Title -->
                                <div class="col-12">
                                    <label for="project_title" class="form-label fw-medium">Project Title *</label>
                                    <input type="text" class="form-control" id="project_title" name="project_title" 
                                           placeholder="e.g., E-commerce Website for Fashion Store" 
                                           value="<?php echo isset($_POST['project_title']) ? htmlspecialchars($_POST['project_title']) : ''; ?>" 
                                           required>
                                    <div class="form-text">Give your project a clear, descriptive title</div>
                                </div>

                                <!-- Category -->
                                <div class="col-md-6">
                                    <label for="category_id" class="form-label fw-medium">Service Category</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">Select a category</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>" 
                                                    <?php echo ($selected_category == $category['id']) ? 'selected' : ''; ?>>
                                                <?php echo $category['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Budget -->
                                <div class="col-md-6">
                                    <label for="budget" class="form-label fw-medium">Budget (₵) *</label>
                                    <input type="number" class="form-control" id="budget" name="budget" 
                                           min="50" step="0.01" placeholder="500.00"
                                           value="<?php echo isset($_POST['budget']) ? $_POST['budget'] : ''; ?>" 
                                           required>
                                    <div class="form-text">Minimum budget: ₵50</div>
                                </div>

                                <!-- Deadline -->
                                <div class="col-md-6">
                                    <label for="deadline" class="form-label fw-medium">Deadline *</label>
                                    <input type="date" class="form-control" id="deadline" name="deadline" 
                                           min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>"
                                           value="<?php echo isset($_POST['deadline']) ? $_POST['deadline'] : ''; ?>" 
                                           required>
                                </div>

                                <!-- Project Description -->
                                <div class="col-12">
                                    <label for="project_description" class="form-label fw-medium">Project Description *</label>
                                    <textarea class="form-control" id="project_description" name="project_description" 
                                              rows="6" placeholder="Describe your project in detail. Include features, requirements, target audience, design preferences, etc."
                                              required><?php echo isset($_POST['project_description']) ? htmlspecialchars($_POST['project_description']) : ''; ?></textarea>
                                    <div class="form-text">
                                        <span id="charCount">0</span>/2000 characters. Be as detailed as possible to help us understand your needs.
                                    </div>
                                </div>

                                <!-- File Upload -->
                                <div class="col-12">
                                    <label for="project_files" class="form-label fw-medium">Project Files (Optional)</label>
                                    <input type="file" class="form-control" id="project_files" name="project_files[]" 
                                           multiple accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt,.zip">
                                    <div class="form-text">
                                        Upload reference images, documents, or any files that help explain your project. 
                                        Max 5MB per file. Allowed: JPG, PNG, PDF, DOC, TXT, ZIP
                                    </div>
                                    <div id="filePreview" class="mt-2"></div>
                                </div>

                                <!-- Additional Requirements -->
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                                Additional Requirements (Optional)
                                            </h6>
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="mobile_responsive" name="requirements[]" value="mobile_responsive">
                                                        <label class="form-check-label" for="mobile_responsive">
                                                            Mobile Responsive Design
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="seo_optimized" name="requirements[]" value="seo_optimized">
                                                        <label class="form-check-label" for="seo_optimized">
                                                            SEO Optimization
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="cms_integration" name="requirements[]" value="cms_integration">
                                                        <label class="form-check-label" for="cms_integration">
                                                            Content Management System
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="analytics" name="requirements[]" value="analytics">
                                                        <label class="form-check-label" for="analytics">
                                                            Analytics Integration
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <a href="index.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Home
                                        </a>
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-arrow-right me-2"></i>Proceed to Payment
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="card mt-4 bg-light">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-question-circle text-info me-2"></i>
                            Need Help?
                        </h6>
                        <p class="card-text mb-2">
                            Not sure how to describe your project? Here are some tips:
                        </p>
                        <ul class="mb-0">
                            <li>Include the purpose and goals of your project</li>
                            <li>Mention your target audience</li>
                            <li>Describe the features you need</li>
                            <li>Share examples of designs you like</li>
                            <li>Specify any technical requirements</li>
                        </ul>
                        <div class="mt-3">
                            <a href="#" class="btn btn-sm btn-outline-info me-2">
                                <i class="fas fa-comments me-1"></i>Chat with Us
                            </a>
                            <a href="faq.php" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-question me-1"></i>View FAQ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Character counter for description
        const descriptionField = document.getElementById('project_description');
        const charCount = document.getElementById('charCount');
        
        descriptionField.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            if (count > 2000) {
                charCount.classList.add('text-danger');
            } else {
                charCount.classList.remove('text-danger');
            }
        });
        
        // File preview
        document.getElementById('project_files').addEventListener('change', function() {
            const filePreview = document.getElementById('filePreview');
            filePreview.innerHTML = '';
            
            if (this.files.length > 0) {
                const fileList = document.createElement('div');
                fileList.className = 'mt-2';
                
                Array.from(this.files).forEach(file => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'badge bg-light text-dark me-2 mb-1';
                    fileItem.innerHTML = `
                        <i class="fas fa-file me-1"></i>
                        ${file.name} (${formatFileSize(file.size)})
                    `;
                    fileList.appendChild(fileItem);
                });
                
                filePreview.appendChild(fileList);
            }
        });
        
        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Form validation
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            const title = document.getElementById('project_title').value.trim();
            const description = document.getElementById('project_description').value.trim();
            const budget = parseFloat(document.getElementById('budget').value);
            const deadline = document.getElementById('deadline').value;
            
            if (!title || !description || !budget || !deadline) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return;
            }
            
            if (budget < 50) {
                e.preventDefault();
                alert('Minimum budget is ₵50.');
                return;
            }
            
            if (description.length > 2000) {
                e.preventDefault();
                alert('Project description must be less than 2000 characters.');
                return;
            }
            
            // Check file sizes
            const files = document.getElementById('project_files').files;
            for (let file of files) {
                if (file.size > 5 * 1024 * 1024) { // 5MB
                    e.preventDefault();
                    alert(`File "${file.name}" is too large. Maximum size is 5MB.`);
                    return;
                }
            }
        });
        
        // Auto-resize textarea
        descriptionField.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        // Initialize character count
        charCount.textContent = descriptionField.value.length;
    </script>
</body>
</html>