<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('auth/login.php');
}

// Get order ID from URL
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

if (!$order_id) {
    redirect('orders.php');
}

$db = getDB();

// Get order details
$stmt = $db->prepare("
    SELECT o.*, c.name as category_name, u.full_name, u.email 
    FROM orders o 
    LEFT JOIN categories c ON o.category_id = c.id 
    LEFT JOIN users u ON o.user_id = u.id 
    WHERE o.id = ? AND (o.user_id = ? OR ? = 1)
");
$stmt->execute([$order_id, $_SESSION['user_id'], isAdmin() ? 1 : 0]);
$order = $stmt->fetch();

if (!$order) {
    redirect('orders.php');
}

// Check for payment success message
$payment_success = isset($_GET['payment']) && $_GET['payment'] === 'success';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Tracking - <?php echo SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <img src="assets/image/logo.png" height="50px" alt="">
                <span style="color: #740ae8;">Spark</span><span style="color: #0eaaf6;">On</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">My Orders</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> <?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                            <li><a class="dropdown-item" href="orders.php"><i class="fas fa-list"></i> My Orders</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <?php if ($payment_success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Payment Successful!</strong> Your order has been confirmed and we'll start working on your project soon.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="fw-bold mb-1">Order #<?php echo $order['id']; ?></h1>
                        <p class="text-muted mb-0">Track your project progress</p>
                    </div>
                    <div>
                        <a href="orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Orders
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Order Progress -->
            <div class="col-lg-8">
                <!-- Progress Timeline -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-route me-2"></i>Project Progress
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <?php
                            $statuses = [
                                'pending' => ['icon' => 'fas fa-clock', 'title' => 'Order Placed', 'desc' => 'Your project request has been submitted'],
                                'paid' => ['icon' => 'fas fa-credit-card', 'title' => 'Payment Confirmed', 'desc' => 'Payment received and order confirmed'],
                                'in_progress' => ['icon' => 'fas fa-cogs', 'title' => 'Work in Progress', 'desc' => 'Our team is working on your project'],
                                'completed' => ['icon' => 'fas fa-check-circle', 'title' => 'Project Completed', 'desc' => 'Your project has been delivered'],
                            ];
                            
                            $current_status = $order['status'];
                            $status_order = ['pending', 'paid', 'in_progress', 'completed'];
                            $current_index = array_search($current_status, $status_order);
                            ?>
                            
                            <?php foreach ($status_order as $index => $status): ?>
                                <?php 
                                $is_completed = $index <= $current_index;
                                $is_current = $status === $current_status;
                                ?>
                                <div class="timeline-item <?php echo $is_completed ? 'completed' : ''; ?> <?php echo $is_current ? 'current' : ''; ?>">
                                    <div class="timeline-marker">
                                        <i class="<?php echo $statuses[$status]['icon']; ?>"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6 class="fw-bold mb-1"><?php echo $statuses[$status]['title']; ?></h6>
                                        <p class="text-muted mb-0"><?php echo $statuses[$status]['desc']; ?></p>
                                        <?php if ($is_current): ?>
                                            <small class="text-success fw-medium">Current Status</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Project Details -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h5 class="fw-bold mb-0">
                            <i class="fas fa-file-alt me-2"></i>Project Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-12">
                                <h6 class="fw-bold"><?php echo htmlspecialchars($order['project_title']); ?></h6>
                                <?php if ($order['category_name']): ?>
                                    <span class="badge bg-primary mb-3"><?php echo $order['category_name']; ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="col-12">
                                <h6 class="fw-bold">Description</h6>
                                <p class="text-muted"><?php echo nl2br(htmlspecialchars($order['project_description'])); ?></p>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="fw-bold">Budget</h6>
                                <p class="text-success fw-bold fs-5"><?php echo formatCurrency($order['budget']); ?></p>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="fw-bold">Deadline</h6>
                                <p class="text-muted"><?php echo date('F j, Y', strtotime($order['deadline'])); ?></p>
                            </div>
                            
                            <?php if ($order['files']): ?>
                                <div class="col-12">
                                    <h6 class="fw-bold">Attached Files</h6>
                                    <div class="d-flex flex-wrap gap-2">
                                        <?php 
                                        $files = json_decode($order['files'], true);
                                        foreach ($files as $file): 
                                        ?>
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-file me-1"></i><?php echo $file['original_name']; ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Admin Notes (if any) -->
                <?php if ($order['admin_notes']): ?>
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="fw-bold mb-0">
                                <i class="fas fa-sticky-note me-2"></i>Project Updates
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($order['admin_notes'])); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Order Summary Sidebar -->
            <div class="col-lg-4">
                <!-- Order Status Card -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-white">
                        <h6 class="fw-bold mb-0">Order Status</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <?php
                            $status_info = [
                                'pending' => ['class' => 'warning', 'icon' => 'clock', 'text' => 'Pending Payment'],
                                'paid' => ['class' => 'info', 'icon' => 'credit-card', 'text' => 'Payment Confirmed'],
                                'in_progress' => ['class' => 'primary', 'icon' => 'cogs', 'text' => 'In Progress'],
                                'completed' => ['class' => 'success', 'icon' => 'check-circle', 'text' => 'Completed'],
                                'cancelled' => ['class' => 'danger', 'icon' => 'times-circle', 'text' => 'Cancelled']
                            ];
                            $current_info = $status_info[$order['status']] ?? $status_info['pending'];
                            ?>
                            <div class="status-icon bg-<?php echo $current_info['class']; ?> bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fas fa-<?php echo $current_info['icon']; ?> fa-2x text-<?php echo $current_info['class']; ?>"></i>
                            </div>
                            <h5 class="fw-bold text-<?php echo $current_info['class']; ?>"><?php echo $current_info['text']; ?></h5>
                        </div>
                        
                        <div class="order-info">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Order Date:</span>
                                <span class="fw-medium"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Payment Status:</span>
                                <span class="badge bg-<?php echo $order['payment_status'] === 'paid' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($order['payment_status']); ?>
                                </span>
                            </div>
                            <?php if ($order['payment_reference']): ?>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Payment Ref:</span>
                                    <small class="text-muted"><?php echo $order['payment_reference']; ?></small>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($order['status'] === 'pending' && $order['payment_status'] === 'pending'): ?>
                            <div class="mt-3">
                                <a href="payment.php?order_id=<?php echo $order['id']; ?>" class="btn btn-success w-100">
                                    <i class="fas fa-credit-card me-2"></i>Complete Payment
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Contact Support -->
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h6 class="fw-bold mb-0">Need Help?</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">Have questions about your project? Our support team is here to help.</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="toggleChat()">
                                <i class="fas fa-comments me-2"></i>Live Chat
                            </button>
                            <a href="mailto:<?php echo SITE_EMAIL; ?>" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-envelope me-2"></i>Email Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Bot -->
    <div class="chat-widget" id="chatWidget">
        <div class="chat-toggle" onclick="toggleChat()">
            <i class="fas fa-comments"></i>
        </div>
        
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <h6 class="mb-0">SparkOn Support</h6>
                <button class="btn-close btn-close-white" onclick="toggleChat()"></button>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="bot-message">
                    <p>Hi! I can help you with questions about your order #<?php echo $order['id']; ?>. How can I assist you?</p>
                </div>
            </div>
            <div class="chat-input">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Type your message..." id="chatInput" onkeypress="handleChatKeyPress(event)">
                    <button class="btn btn-success" onclick="sendChatMessage()">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>
    
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 12px;
        }
        
        .timeline-item.completed .timeline-marker {
            background: #740ae8;
            color: white;
        }
        
        .timeline-item.current .timeline-marker {
            background: #0eaaf6;
            color: white;
            box-shadow: 0 0 0 4px rgba(14, 170, 246, 0.2);
        }
        
        .timeline-content {
            padding-left: 20px;
        }
        
        .timeline-item.completed .timeline-content h6 {
            color: #740ae8;
        }
        
        .timeline-item.current .timeline-content h6 {
            color: #0eaaf6;
        }
    </style>
</body>
</html>