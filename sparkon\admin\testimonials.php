<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../auth/login.php');
}

$db = getDB();

// Handle testimonial actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        try {
            switch ($action) {
                case 'add_testimonial':
                    $client_name = sanitize($_POST['client_name']);
                    $client_position = sanitize($_POST['client_position']);
                    $client_company = sanitize($_POST['client_company']);
                    $testimonial = sanitize($_POST['testimonial']);
                    $rating = (int)$_POST['rating'];
                    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                    
                    if (empty($client_name) || empty($testimonial)) {
                        $error = 'Client name and testimonial are required.';
                    } else {
                        $stmt = $db->prepare("INSERT INTO testimonials (client_name, client_position, client_company, testimonial, rating, is_featured) VALUES (?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$client_name, $client_position, $client_company, $testimonial, $rating, $is_featured]);
                        $success = 'Testimonial added successfully.';
                    }
                    break;
                    
                case 'edit_testimonial':
                    $testimonial_id = (int)$_POST['testimonial_id'];
                    $client_name = sanitize($_POST['client_name']);
                    $client_position = sanitize($_POST['client_position']);
                    $client_company = sanitize($_POST['client_company']);
                    $testimonial = sanitize($_POST['testimonial']);
                    $rating = (int)$_POST['rating'];
                    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                    
                    if (empty($client_name) || empty($testimonial)) {
                        $error = 'Client name and testimonial are required.';
                    } else {
                        $stmt = $db->prepare("UPDATE testimonials SET client_name = ?, client_position = ?, client_company = ?, testimonial = ?, rating = ?, is_featured = ? WHERE id = ?");
                        $stmt->execute([$client_name, $client_position, $client_company, $testimonial, $rating, $is_featured, $testimonial_id]);
                        $success = 'Testimonial updated successfully.';
                    }
                    break;
                    
                case 'delete_testimonial':
                    $testimonial_id = (int)$_POST['testimonial_id'];
                    $stmt = $db->prepare("DELETE FROM testimonials WHERE id = ?");
                    $stmt->execute([$testimonial_id]);
                    $success = 'Testimonial deleted successfully.';
                    break;
                    
                case 'toggle_featured':
                    $testimonial_id = (int)$_POST['testimonial_id'];
                    $stmt = $db->prepare("UPDATE testimonials SET is_featured = NOT is_featured WHERE id = ?");
                    $stmt->execute([$testimonial_id]);
                    $success = 'Featured status updated successfully.';
                    break;
            }
        } catch (Exception $e) {
            $error = 'Failed to perform action: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$featured_filter = isset($_GET['featured']) ? sanitize($_GET['featured']) : '';
$rating_filter = isset($_GET['rating']) ? (int)$_GET['rating'] : 0;

// Build query with filters
$where_conditions = ['1=1'];
$params = [];

if ($featured_filter !== '') {
    $where_conditions[] = 'is_featured = ?';
    $params[] = $featured_filter === 'yes' ? 1 : 0;
}

if ($rating_filter) {
    $where_conditions[] = 'rating = ?';
    $params[] = $rating_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get testimonials with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 12;
$offset = ($page - 1) * $per_page;

$testimonials_stmt = $db->prepare("
    SELECT * FROM testimonials 
    WHERE {$where_clause}
    ORDER BY is_featured DESC, created_at DESC 
    LIMIT {$per_page} OFFSET {$offset}
");
$testimonials_stmt->execute($params);
$testimonials = $testimonials_stmt->fetchAll();

// Get total count for pagination
$count_stmt = $db->prepare("SELECT COUNT(*) as total FROM testimonials WHERE {$where_clause}");
$count_stmt->execute($params);
$total_testimonials = $count_stmt->fetch()['total'];
$total_pages = ceil($total_testimonials / $per_page);

// Get testimonial statistics
$stats_stmt = $db->prepare("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_featured = 1 THEN 1 END) as featured,
        AVG(rating) as avg_rating,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent
    FROM testimonials
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testimonials Management - <?php echo SITE_NAME; ?> Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Macan:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #740ae8 0%, #0eaaf6 100%);
        }
        
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .testimonial-card {
            transition: transform 0.3s ease;
        }
        
        .testimonial-card:hover {
            transform: translateY(-5px);
        }
        
        .client-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 1.5rem;
        }
        
        .rating-stars {
            color: #ffc107;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <span style="color: #0eaaf6;">Spark</span>On
                        </h4>
                        <small class="text-white-50">Admin Panel</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>Orders
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        <a class="nav-link" href="contacts.php">
                            <i class="fas fa-envelope me-2"></i>Contacts
                        </a>
                        <a class="nav-link" href="categories.php">
                            <i class="fas fa-tags me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="portfolio.php">
                            <i class="fas fa-briefcase me-2"></i>Portfolio
                        </a>
                        <a class="nav-link active" href="testimonials.php">
                            <i class="fas fa-star me-2"></i>Testimonials
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        
                        <hr class="text-white-50">
                        
                        <a class="nav-link" href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Site
                        </a>
                        <a class="nav-link" href="../auth/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <!-- Header -->
                <div class="admin-header p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="fw-bold mb-0">Testimonials Management</h2>
                            <small class="text-muted">Manage client testimonials and reviews</small>
                        </div>
                        <div>
                            <button class="btn btn-success" onclick="addTestimonial()">
                                <i class="fas fa-plus me-2"></i>Add Testimonial
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics Cards -->
                <div class="row g-4 mb-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['total']; ?></h4>
                                <small class="text-muted">Total Testimonials</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-heart fa-2x text-danger mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['featured']; ?></h4>
                                <small class="text-muted">Featured</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo number_format($stats['avg_rating'], 1); ?></h4>
                                <small class="text-muted">Average Rating</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h4 class="fw-bold mb-0"><?php echo $stats['recent']; ?></h4>
                                <small class="text-muted">Recent (30 days)</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Featured Status</label>
                                <select class="form-select" name="featured">
                                    <option value="">All Testimonials</option>
                                    <option value="yes" <?php echo $featured_filter === 'yes' ? 'selected' : ''; ?>>Featured Only</option>
                                    <option value="no" <?php echo $featured_filter === 'no' ? 'selected' : ''; ?>>Non-Featured</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Filter by Rating</label>
                                <select class="form-select" name="rating">
                                    <option value="">All Ratings</option>
                                    <option value="5" <?php echo $rating_filter == 5 ? 'selected' : ''; ?>>5 Stars</option>
                                    <option value="4" <?php echo $rating_filter == 4 ? 'selected' : ''; ?>>4 Stars</option>
                                    <option value="3" <?php echo $rating_filter == 3 ? 'selected' : ''; ?>>3 Stars</option>
                                    <option value="2" <?php echo $rating_filter == 2 ? 'selected' : ''; ?>>2 Stars</option>
                                    <option value="1" <?php echo $rating_filter == 1 ? 'selected' : ''; ?>>1 Star</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-filter me-1"></i>Filter
                                </button>
                                <a href="testimonials.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                            <div class="col-md-2 d-flex align-items-end justify-content-end">
                                <small class="text-muted"><?php echo $total_testimonials; ?> testimonials found</small>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Testimonials Grid -->
                <div class="row g-4">
                    <?php foreach ($testimonials as $testimonial): ?>
                        <div class="col-lg-6">
                            <div class="card testimonial-card shadow-sm h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-start mb-3">
                                        <div class="client-avatar bg-primary me-3">
                                            <?php echo strtoupper(substr($testimonial['client_name'], 0, 1)); ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="fw-bold mb-0"><?php echo htmlspecialchars($testimonial['client_name']); ?></h6>
                                            <?php if ($testimonial['client_position']): ?>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($testimonial['client_position']); ?>
                                                    <?php if ($testimonial['client_company']): ?>
                                                        at <?php echo htmlspecialchars($testimonial['client_company']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                        <?php if ($testimonial['is_featured']): ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-star me-1"></i>Featured
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="rating-stars mb-2">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?php echo $i <= $testimonial['rating'] ? '' : 'text-muted'; ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    
                                    <p class="text-muted mb-3">
                                        "<?php echo htmlspecialchars($testimonial['testimonial']); ?>"
                                    </p>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <?php echo date('M j, Y', strtotime($testimonial['created_at'])); ?>
                                        </small>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="editTestimonial(<?php echo htmlspecialchars(json_encode($testimonial)); ?>)" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="toggleFeatured(<?php echo $testimonial['id']; ?>)" title="Toggle Featured">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteTestimonial(<?php echo $testimonial['id']; ?>, '<?php echo htmlspecialchars($testimonial['client_name']); ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <?php if (empty($testimonials)): ?>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No testimonials found</h5>
                                <p class="text-muted mb-3">
                                    <?php if ($featured_filter !== '' || $rating_filter): ?>
                                        Try adjusting your filters or add new testimonials.
                                    <?php else: ?>
                                        Start building trust by adding your first client testimonial.
                                    <?php endif; ?>
                                </p>
                                <button class="btn btn-success" onclick="addTestimonial()">
                                    <i class="fas fa-plus me-2"></i>Add Testimonial
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Testimonials pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&featured=<?php echo $featured_filter; ?>&rating=<?php echo $rating_filter; ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&featured=<?php echo $featured_filter; ?>&rating=<?php echo $rating_filter; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&featured=<?php echo $featured_filter; ?>&rating=<?php echo $rating_filter; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Add/Edit Testimonial Modal -->
    <div class="modal fade" id="testimonialModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="testimonialModalTitle">Add Testimonial</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="testimonialForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" id="testimonialAction" value="add_testimonial">
                        <input type="hidden" name="testimonial_id" id="testimonialId">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Client Name *</label>
                                <input type="text" class="form-control" name="client_name" id="clientName" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Rating *</label>
                                <select class="form-select" name="rating" id="clientRating" required>
                                    <option value="">Select Rating</option>
                                    <option value="5">5 Stars - Excellent</option>
                                    <option value="4">4 Stars - Very Good</option>
                                    <option value="3">3 Stars - Good</option>
                                    <option value="2">2 Stars - Fair</option>
                                    <option value="1">1 Star - Poor</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Position/Title</label>
                                <input type="text" class="form-control" name="client_position" id="clientPosition" 
                                       placeholder="e.g., CEO, Marketing Director">
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">Company</label>
                                <input type="text" class="form-control" name="client_company" id="clientCompany" 
                                       placeholder="e.g., TechCorp Inc.">
                            </div>
                            
                            <div class="col-12">
                                <label class="form-label">Testimonial *</label>
                                <textarea class="form-control" name="testimonial" id="testimonialText" rows="4" 
                                          placeholder="Enter the client's testimonial..." required></textarea>
                                <div class="form-text">Write in first person as if the client is speaking</div>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_featured" id="testimonialFeatured">
                                    <label class="form-check-label" for="testimonialFeatured">
                                        Featured Testimonial (will be displayed on homepage)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success" id="testimonialSubmitBtn">Add Testimonial</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the testimonial from "<span id="deleteClientName"></span>"?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete_testimonial">
                        <input type="hidden" name="testimonial_id" id="deleteTestimonialId">
                        <button type="submit" class="btn btn-danger">Delete Testimonial</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function addTestimonial() {
            document.getElementById('testimonialModalTitle').textContent = 'Add Testimonial';
            document.getElementById('testimonialAction').value = 'add_testimonial';
            document.getElementById('testimonialSubmitBtn').textContent = 'Add Testimonial';
            document.getElementById('testimonialForm').reset();
            
            const modal = new bootstrap.Modal(document.getElementById('testimonialModal'));
            modal.show();
        }
        
        function editTestimonial(testimonial) {
            document.getElementById('testimonialModalTitle').textContent = 'Edit Testimonial';
            document.getElementById('testimonialAction').value = 'edit_testimonial';
            document.getElementById('testimonialSubmitBtn').textContent = 'Update Testimonial';
            document.getElementById('testimonialId').value = testimonial.id;
            document.getElementById('clientName').value = testimonial.client_name;
            document.getElementById('clientPosition').value = testimonial.client_position || '';
            document.getElementById('clientCompany').value = testimonial.client_company || '';
            document.getElementById('testimonialText').value = testimonial.testimonial;
            document.getElementById('clientRating').value = testimonial.rating;
            document.getElementById('testimonialFeatured').checked = testimonial.is_featured == 1;
            
            const modal = new bootstrap.Modal(document.getElementById('testimonialModal'));
            modal.show();
        }
        
        function deleteTestimonial(testimonialId, clientName) {
            document.getElementById('deleteTestimonialId').value = testimonialId;
            document.getElementById('deleteClientName').textContent = clientName;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
        
        function toggleFeatured(testimonialId) {
            if (confirm('Toggle featured status for this testimonial?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_featured">
                    <input type="hidden" name="testimonial_id" value="${testimonialId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Form validation
        document.getElementById('testimonialForm').addEventListener('submit', function(e) {
            const clientName = document.getElementById('clientName').value.trim();
            const testimonialText = document.getElementById('testimonialText').value.trim();
            const rating = document.getElementById('clientRating').value;
            
            if (!clientName || !testimonialText || !rating) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return;
            }
        });
    </script>
</body>
</html>